# 💡 **ملف الأمثلة العملية - المحاضرة الثانية في التشفير**
## **حلول مفصلة وأمثلة تطبيقية باستخدام طريقة CICS**

---

## 🧭 **قائمة التنقل**

| 🏠 [الفهرس الرئيسي](./index.md) | 📚 [التلخيص النظري](./summary.md) | 📊 [المراجع السريعة](./quick-reference.md) | 📝 [التمارين](./exercises.md) |
|:---:|:---:|:---:|:---:|
| **البداية** | **النظرية** | **المرجع** | **التدريب** |

### **🎯 أنت هنا: الأمثلة العملية المحلولة**
- **الهدف**: تطبيق المفاهيم النظرية عملياً
- **المحتوى**: 24 مثال محلول مع القوانين والتطبيق
- **الوقت المقترح**: 90-120 دقيقة للدراسة التفاعلية
- **السابق المقترح**: [التلخيص النظري](./summary.md) للفهم الأساسي
- **التالي المقترح**: [التمارين](./exercises.md) للتدريب

---

## 📋 **فهرس الأمثلة**

| القسم | نوع المثال | عدد الأمثلة |
|-------|-------------|-------------|
| 🔐 | [أمثلة التشفير الأساسي](#أمثلة-التشفير-الأساسي) | 4 |
| 🧮 | [أمثلة الحساب الصحيح](#أمثلة-الحساب-الصحيح) | 5 |
| 🔍 | [أمثلة خوارزمية إقليدس](#أمثلة-خوارزمية-إقليدس) | 4 |
| 🔄 | [أمثلة الحساب النمطي](#أمثلة-الحساب-النمطي) | 6 |
| ↔️ | [أمثلة المعكوسات](#أمثلة-المعكوسات) | 5 |

---

## 🔐 **أمثلة التشفير الأساسي**

### **💡 مثال 1: تشفير قيصر الأساسي**

#### **📐 القوانين المستخدمة**
1. **قانون تشفير قيصر**: `C = (P + k) mod n`
   - حيث C: الحرف المشفر، P: الحرف الأصلي، k: مفتاح الإزاحة، n: حجم الأبجدية
2. **قانون فك تشفير قيصر**: `P = (C - k) mod n`
3. **تحويل الأحرف للأرقام**: `A=0, B=1, ..., Z=25`

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: عند تشفير نص باستخدام إزاحة ثابتة لجميع الأحرف
- **الشروط**: k يجب أن يكون عدد صحيح، n > 0
- **خطوات التطبيق**:
  1. تحويل كل حرف لرقم مقابل
  2. تطبيق قانون الإزاحة مع العامل النمطي
  3. تحويل النتيجة لحرف

#### **🔗 مرجع سريع**
- راجع جدول "قوانين الحساب النمطي" في `quick-reference.md`
- راجع قسم "تصنيفات التشفير" للمقارنة مع أنواع أخرى

```
🎯 المسألة: شفّر النص "CRYPTOGRAPHY" باستخدام تشفير قيصر مع إزاحة 3

📋 المعطيات:
• النص الأصلي: CRYPTOGRAPHY
• نوع التشفير: قيصر (Caesar Cipher)
• مفتاح الإزاحة: k = 3
• الأبجدية: A-Z (26 حرف)

🔧 الحل خطوة بخطوة:

الخطوة 1: تحويل الأحرف إلى أرقام (A=0, B=1, ..., Z=25)
C=2, R=17, Y=24, P=15, T=19, O=14, G=6, R=17, A=0, P=15, H=7, Y=24

الخطوة 2: تطبيق الإزاحة (mod 26)
C: (2 + 3) mod 26 = 5 → F
R: (17 + 3) mod 26 = 20 → U
Y: (24 + 3) mod 26 = 1 → B
P: (15 + 3) mod 26 = 18 → S
T: (19 + 3) mod 26 = 22 → W
O: (14 + 3) mod 26 = 17 → R
G: (6 + 3) mod 26 = 9 → J
R: (17 + 3) mod 26 = 20 → U
A: (0 + 3) mod 26 = 3 → D
P: (15 + 3) mod 26 = 18 → S
H: (7 + 3) mod 26 = 10 → K
Y: (24 + 3) mod 26 = 1 → B

✅ النتيجة النهائية: "FUBSWRJUDSKB"

🔍 التحقق: فك التشفير بإزاحة -3
F: (5 - 3) mod 26 = 2 → C ✓
U: (20 - 3) mod 26 = 17 → R ✓
... وهكذا
```

### **💡 مثال 2: تشفير قيصر مع إزاحة سالبة**

#### **📐 القوانين المستخدمة**
1. **قانون تشفير قيصر مع إزاحة سالبة**: `C = (P + k) mod n` حيث k سالب
2. **قانون التعامل مع النتائج السالبة**: إذا كان `(a + k) < 0`، فإن `(a + k) mod n = (a + k + n) mod n`
3. **قانون عام للعامل النمطي مع الأعداد السالبة**: `(-x) mod n = n - (x mod n)`

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: عند وجود إزاحة سالبة أو عند فك التشفير
- **الشروط**: يجب التعامل مع النتائج السالبة بإضافة n
- **خطوات التطبيق**:
  1. تطبيق الإزاحة العادية
  2. إذا كانت النتيجة سالبة، أضف n
  3. تأكد أن النتيجة في المجال [0, n-1]

#### **🔗 مرجع سريع**
- راجع قسم "قوانين الحساب النمطي" في `quick-reference.md`
- راجع جدول "التعامل مع الأعداد السالبة في mod"

```
🎯 المسألة: شفّر النص "HELLO" باستخدام إزاحة -5

📋 المعطيات:
• النص الأصلي: HELLO
• مفتاح الإزاحة: k = -5
• الأبجدية: A-Z (26 حرف)

🔧 الحل:

الخطوة 1: تحويل الأحرف إلى أرقام
H=7, E=4, L=11, L=11, O=14

الخطوة 2: تطبيق الإزاحة السالبة
H: (7 - 5) mod 26 = 2 → C
E: (4 - 5) mod 26 = -1 mod 26 = 25 → Z
L: (11 - 5) mod 26 = 6 → G
L: (11 - 5) mod 26 = 6 → G
O: (14 - 5) mod 26 = 9 → J

⚠️ ملاحظة: للتعامل مع النتائج السالبة
-1 mod 26 = -1 + 26 = 25

✅ النتيجة: "CZGGJ"
```

### **💡 مثال 3: تحديد نوع التشفير**

#### **📐 القوانين المستخدمة**
1. **قانون التصنيف حسب المفاتيح**:
   - مفتاح واحد → متماثل
   - مفتاحان → غير متماثل
   - بدون مفتاح → هاش
2. **معايير الأمان**: قوة التشفير تعتمد على صعوبة المسائل الرياضية
3. **معايير الكفاءة**: السرعة تتناسب عكسياً مع مستوى الأمان

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: عند اختيار نوع التشفير المناسب للتطبيق
- **معايير الاختيار**:
  - حجم البيانات (كبير → متماثل)
  - توزيع المفاتيح (صعب → غير متماثل)
  - التحقق من السلامة (هاش)
- **خطوات التحليل**:
  1. تحديد عدد المفاتيح المطلوبة
  2. تحديد نوع العملية (تشفير/توقيع/تحقق)
  3. اختيار النوع المناسب

#### **🔗 مرجع سريع**
- راجع جدول "تصنيفات التشفير" في `quick-reference.md`
- راجع قسم "مقارنة أنواع التشفير" للتفاصيل

```
🎯 المسألة: حدد نوع التشفير المستخدم في الأمثلة التالية:

📋 الحالات:
أ) مفتاح واحد للتشفير وفك التشفير
ب) مفتاحان: عام وخاص
ج) لا يوجد مفتاح، فقط دالة تحويل

🔧 الحل:

الحالة أ: التشفير المتماثل (Symmetric Encryption)
• الخصائص: نفس المفتاح للعمليتين
• الأمثلة: AES، DES، تشفير قيصر
• الاستخدام: تشفير البيانات الكبيرة

الحالة ب: التشفير غير المتماثل (Asymmetric Encryption)
• الخصائص: مفتاح عام للتشفير، خاص لفك التشفير
• الأمثلة: RSA، ECC، ElGamal
• الاستخدام: تبادل المفاتيح، التوقيع الرقمي

الحالة ج: دالة الهاش (Hash Function)
• الخصائص: تحويل أحادي الاتجاه
• الأمثلة: SHA-256، MD5، SHA-3
• الاستخدام: التحقق من سلامة البيانات
```

### **💡 مثال 4: مقارنة أنواع التشفير**
```
🎯 المسألة: قارن بين التشفير المتماثل وغير المتماثل

📊 جدول المقارنة:

| الخاصية | المتماثل | غير المتماثل |
|---------|----------|---------------|
| **عدد المفاتيح** | 1 | 2 |
| **السرعة** | سريع جداً | بطيء نسبياً |
| **توزيع المفاتيح** | صعب | سهل |
| **حجم المفتاح** | صغير (128-256 بت) | كبير (1024-4096 بت) |
| **الاستخدام الرئيسي** | تشفير البيانات | تبادل المفاتيح |
| **الأمان** | يعتمد على سرية المفتاح | يعتمد على صعوبة المسائل الرياضية |

🔍 التطبيق العملي:
في الأنظمة الحديثة، يُستخدم التشفير غير المتماثل لتبادل مفاتيح التشفير المتماثل، ثم يُستخدم التشفير المتماثل لتشفير البيانات الفعلية.
```

---

## 🧮 **أمثلة الحساب الصحيح**

### **💡 مثال 5: القسمة مع أعداد موجبة**

#### **📐 القوانين المستخدمة**
1. **قانون القسمة الأساسي**: `a = q × n + r`
   - حيث a: المقسوم، q: خارج القسمة، n: المقسوم عليه، r: الباقي
2. **شرط الباقي**: `0 ≤ r < |n|`
3. **قانون التحقق**: `a = q × n + r` يجب أن يعطي المقسوم الأصلي

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: في جميع عمليات القسمة الصحيحة
- **الشروط**: n ≠ 0، والباقي يجب أن يكون في المجال المحدد
- **خطوات التطبيق**:
  1. تنفيذ القسمة للحصول على q و r
  2. التحقق من شرط الباقي
  3. التأكد من صحة المعادلة

#### **🔗 مرجع سريع**
- راجع قسم "قوانين القسمة الصحيحة" في `quick-reference.md`
- راجع جدول "أمثلة القسمة" للمقارنة

```
🎯 المسألة: احسب خارج القسمة والباقي لـ 157 ÷ 12

📋 المعطيات:
• المقسوم: a = 157
• المقسوم عليه: n = 12

🔧 الحل:

الخطوة 1: تطبيق القسمة
157 ÷ 12 = 13 والباقي 1

الخطوة 2: التحقق من الشرط
0 ≤ 1 < 12 ✓

الخطوة 3: كتابة المعادلة
157 = 13 × 12 + 1

🔍 التحقق:
13 × 12 + 1 = 156 + 1 = 157 ✓

✅ النتيجة: q = 13, r = 1
```

### **💡 مثال 6: القسمة مع عدد سالب (حالة معقدة)**

#### **📐 القوانين المستخدمة**
1. **قانون القسمة مع الأعداد السالبة**: `a = q × n + r` حيث a سالب
2. **قانون تصحيح الباقي السالب**:
   - إذا كان `r < 0`، فإن `q_جديد = q - 1` و `r_جديد = r + n`
3. **شرط الباقي الإجباري**: `0 ≤ r < n` دائماً، حتى مع الأعداد السالبة

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: عند قسمة عدد سالب أو حساب mod لعدد سالب
- **الشروط**: يجب تصحيح الباقي السالب دائماً
- **خطوات التطبيق**:
  1. تنفيذ القسمة العادية
  2. فحص إشارة الباقي
  3. تصحيح الباقي السالب بإضافة المقسوم عليه
  4. تعديل خارج القسمة وفقاً لذلك

#### **🔗 مرجع سريع**
- راجع قسم "التعامل مع الأعداد السالبة في mod" في `quick-reference.md`
- راجع جدول "أمثلة القسمة مع الأعداد السالبة"

```
🎯 المسألة: احسب -47 mod 15

📋 المعطيات:
• المقسوم: a = -47
• المقسوم عليه: n = 15

🔧 الحل:

الخطوة 1: القسمة الأولية
-47 ÷ 15 = -3 والباقي -2
تحقق: -47 = (-3) × 15 + (-2) = -45 - 2 = -47 ✓

الخطوة 2: تصحيح الباقي السالب
⚠️ مشكلة: r = -2 < 0

الخطوة 3: التصحيح
q = q - 1 = -3 - 1 = -4
r = r + n = -2 + 15 = 13

الخطوة 4: التحقق النهائي
-47 = (-4) × 15 + 13 = -60 + 13 = -47 ✓
والشرط: 0 ≤ 13 < 15 ✓

✅ النتيجة: -47 mod 15 = 13
```

### **💡 مثال 7: تطبيق خصائص القابلية للقسمة**
```
🎯 المسألة: إذا كان 6|18 و 6|24، فهل 6|(3×18 + 2×24)؟

📋 المعطيات:
• 6|18 (6 يقسم 18)
• 6|24 (6 يقسم 24)
• m = 3, n = 2

🔧 الحل:

الخطوة 1: تطبيق الخاصية الرابعة
إذا كان a|b و a|c، فإن a|(m×b + n×c)

الخطوة 2: التطبيق على المسألة
بما أن 6|18 و 6|24
إذن 6|(3×18 + 2×24)

الخطوة 3: التحقق العملي
3×18 + 2×24 = 54 + 48 = 102
102 ÷ 6 = 17 (بدون باقي)

✅ النتيجة: نعم، 6|102
```

### **💡 مثال 8: إيجاد جميع قواسم عدد**
```
🎯 المسألة: أوجد جميع قواسم العدد 24

🔧 الحل:

الخطوة 1: التحليل إلى عوامل أولية
24 = 2³ × 3¹

الخطوة 2: إيجاد جميع القواسم
القواسم = {2^i × 3^j : 0 ≤ i ≤ 3, 0 ≤ j ≤ 1}

الخطوة 3: حساب القواسم
2⁰ × 3⁰ = 1
2¹ × 3⁰ = 2
2² × 3⁰ = 4
2³ × 3⁰ = 8
2⁰ × 3¹ = 3
2¹ × 3¹ = 6
2² × 3¹ = 12
2³ × 3¹ = 24

✅ النتيجة: قواسم 24 هي {1, 2, 3, 4, 6, 8, 12, 24}

🔍 التحقق: عدد القواسم = (3+1)(1+1) = 8 ✓
```

### **💡 مثال 9: مسألة تطبيقية**
```
🎯 المسألة: في نظام تشفير، نريد تقسيم رسالة طولها 1000 حرف إلى كتل حجم كل منها 17 حرف. كم كتلة كاملة سنحصل عليها؟ وكم حرف سيبقى؟

📋 المعطيات:
• طول الرسالة: 1000 حرف
• حجم الكتلة: 17 حرف

🔧 الحل:

الخطوة 1: تطبيق القسمة
1000 ÷ 17 = 58 والباقي 14

الخطوة 2: التحقق
58 × 17 + 14 = 986 + 14 = 1000 ✓

✅ النتيجة:
• عدد الكتل الكاملة: 58 كتلة
• عدد الأحرف المتبقية: 14 حرف

💡 التطبيق العملي:
سنحتاج إلى إضافة 3 أحرف حشو (padding) للكتلة الأخيرة لتصبح 17 حرف.
```

---

## 🔍 **أمثلة خوارزمية إقليدس**

### **💡 مثال 10: GCD لعددين صغيرين**

#### **📐 القوانين المستخدمة**
1. **خوارزمية إقليدس الأساسية**: `gcd(a, b) = gcd(b, a mod b)`
2. **حالة التوقف**: `gcd(a, 0) = a`
3. **خاصية التبديل**: `gcd(a, b) = gcd(b, a)`
4. **قانون التحقق**: إذا كان `gcd(a, b) = d`، فإن `a = d × m` و `b = d × n` حيث `gcd(m, n) = 1`

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: لإيجاد القاسم المشترك الأكبر لأي عددين صحيحين
- **الشروط**: العددان يجب أن يكونا موجبين (أو نأخذ القيمة المطلقة)
- **خطوات التطبيق**:
  1. تطبيق قانون القسمة: a = q × b + r
  2. استبدال: gcd(a, b) = gcd(b, r)
  3. تكرار حتى الوصول لـ gcd(x, 0) = x

#### **🔗 مرجع سريع**
- راجع قسم "خوارزمية إقليدس" في `quick-reference.md`
- راجع جدول "أمثلة GCD للأعداد الصغيرة"

```
🎯 المسألة: احسب gcd(48, 18)

🔧 الحل:

الخطوة 1: gcd(48, 18)
48 = 2 × 18 + 12
⟹ gcd(48, 18) = gcd(18, 12)

الخطوة 2: gcd(18, 12)
18 = 1 × 12 + 6
⟹ gcd(18, 12) = gcd(12, 6)

الخطوة 3: gcd(12, 6)
12 = 2 × 6 + 0
⟹ gcd(12, 6) = gcd(6, 0) = 6

✅ النتيجة: gcd(48, 18) = 6

🔍 التحقق:
• 48 = 6 × 8 ✓
• 18 = 6 × 3 ✓
• gcd(8, 3) = 1 ✓ (أوليان نسبياً)
```

### **💡 مثال 11: GCD مع خوارزمية إقليدس الموسعة**

#### **📐 القوانين المستخدمة**
1. **خوارزمية إقليدس الموسعة**: إيجاد `gcd(a, b) = s×a + t×b`
2. **مبدأ العمل العكسي**: البدء من آخر معادلة والتعويض للخلف
3. **قانون التعويض**: استبدال كل باقي بالمعادلة التي أنتجته
4. **معادلة بيزو**: لكل عددين a, b يوجد s, t بحيث `gcd(a, b) = s×a + t×b`

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: لإيجاد المعكوسات الضربية وحل المعادلات الديوفانتية
- **الشروط**: يجب تطبيق خوارزمية إقليدس أولاً
- **خطوات التطبيق**:
  1. تطبيق خوارزمية إقليدس العادية
  2. البدء من آخر معادلة قبل الوصول للصفر
  3. التعويض العكسي خطوة بخطوة
  4. جمع المعاملات للحصول على s و t

#### **🔗 مرجع سريع**
- راجع قسم "خوارزمية إقليدس الموسعة" في `quick-reference.md`
- راجع جدول "أمثلة Extended GCD"

```
🎯 المسألة: احسب gcd(35, 15) وأوجد s, t بحيث gcd(35, 15) = s×35 + t×15

🔧 الحل:

الخطوة 1: تطبيق خوارزمية إقليدس
35 = 2 × 15 + 5
15 = 3 × 5 + 0
⟹ gcd(35, 15) = 5

الخطوة 2: العمل بالاتجاه العكسي
5 = 35 - 2 × 15
⟹ 5 = 1 × 35 + (-2) × 15

✅ النتيجة:
• gcd(35, 15) = 5
• s = 1, t = -2

🔍 التحقق: 1 × 35 + (-2) × 15 = 35 - 30 = 5 ✓
```

### **💡 مثال 12: حالة الأعداد الأولية النسبية**
```
🎯 المسألة: احسب gcd(17, 13)

🔧 الحل:

الخطوة 1: gcd(17, 13)
17 = 1 × 13 + 4
⟹ gcd(17, 13) = gcd(13, 4)

الخطوة 2: gcd(13, 4)
13 = 3 × 4 + 1
⟹ gcd(13, 4) = gcd(4, 1)

الخطوة 3: gcd(4, 1)
4 = 4 × 1 + 0
⟹ gcd(4, 1) = gcd(1, 0) = 1

✅ النتيجة: gcd(17, 13) = 1

💡 الاستنتاج: 17 و 13 أوليان نسبياً
هذا يعني أن لكل منهما معكوس ضربي في المجموعة المناسبة.
```

### **💡 مثال 13: تطبيق عملي في التشفير**
```
🎯 المسألة: في خوارزمية RSA، نحتاج للتأكد من أن gcd(e, φ(n)) = 1 حيث e = 65537 و φ(n) = 3120. تحقق من هذا الشرط.

📋 المعطيات:
• e = 65537 (الأس العام)
• φ(n) = 3120 (دالة أويلر)

🔧 الحل:

الخطوة 1: تطبيق خوارزمية إقليدس
65537 = 21 × 3120 + 17
3120 = 183 × 17 + 9
17 = 1 × 9 + 8
9 = 1 × 8 + 1
8 = 8 × 1 + 0

✅ النتيجة: gcd(65537, 3120) = 1

🔍 الاستنتاج: الشرط محقق، يمكن استخدام e = 65537 كأس عام في RSA.

💡 الأهمية: هذا الشرط ضروري لضمان وجود المعكوس الضربي لـ e في Zφ(n).
```

---

## 🔄 **أمثلة الحساب النمطي**

### **💡 مثال 14: عمليات أساسية في Zn**
```
🎯 المسألة: نفذ العمليات التالية في Z₁₃:
أ) (8 + 7) mod 13
ب) (11 - 15) mod 13
ج) (9 × 6) mod 13

🔧 الحل:

أ) الجمع في Z₁₃:
(8 + 7) mod 13 = 15 mod 13 = 2

ب) الطرح في Z₁₃:
(11 - 15) mod 13 = (-4) mod 13 = 9
(لأن -4 + 13 = 9)

ج) الضرب في Z₁₃:
(9 × 6) mod 13 = 54 mod 13 = 2

✅ النتائج: أ) 2، ب) 9، ج) 2
```

### **💡 مثال 15: تطبيق خصائص العامل النمطي**

#### **📐 القوانين المستخدمة**
1. **خاصية الضرب النمطي**: `(a × b) mod n = ((a mod n) × (b mod n)) mod n`
2. **خاصية الأس النمطي**: `a^k mod n = ((a mod n)^k) mod n`
3. **قانون التبسيط**: تبسيط الأسس قبل الحساب لتجنب الأرقام الكبيرة
4. **خاصية التجميع**: `(a^m × b^n) mod p = ((a^m mod p) × (b^n mod p)) mod p`

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: عند حساب عمليات معقدة مع أرقام كبيرة في الحساب النمطي
- **الشروط**: n > 0، ويفضل تبسيط الأسس أولاً
- **خطوات التطبيق**:
  1. تبسيط كل أساس باستخدام mod
  2. حساب كل أس على حدة
  3. ضرب النتائج مع تطبيق mod
  4. الحصول على النتيجة النهائية

#### **🔗 مرجع سريع**
- راجع جدول "خصائص العامل النمطي" في `quick-reference.md`
- راجع قسم "حساب القوى الكبيرة" للطرق المتقدمة

```
🎯 المسألة: احسب (123⁴ × 456³) mod 7 بطريقة فعالة

📋 المعطيات:
• الأساس الأول: 123
• الأساس الثاني: 456
• الأسس: 4 و 3
• المعامل: 7

🔧 الحل:

الخطوة 1: تبسيط الأسس
123 mod 7 = 4 (لأن 123 = 17 × 7 + 4)
456 mod 7 = 1 (لأن 456 = 65 × 7 + 1)

الخطوة 2: تطبيق خاصية الأس
123⁴ mod 7 = 4⁴ mod 7
456³ mod 7 = 1³ mod 7 = 1

الخطوة 3: حساب 4⁴ mod 7
4¹ mod 7 = 4
4² mod 7 = 16 mod 7 = 2
4³ mod 7 = (4² × 4) mod 7 = (2 × 4) mod 7 = 1
4⁴ mod 7 = (4³ × 4) mod 7 = (1 × 4) mod 7 = 4

الخطوة 4: الضرب النهائي
(123⁴ × 456³) mod 7 = (4 × 1) mod 7 = 4

✅ النتيجة: 4
```

### **💡 مثال 16: حساب القوى الكبيرة**
```
🎯 المسألة: احسب 2¹⁰⁰ mod 13 باستخدام التربيع المتكرر

🔧 الحل:

الخطوة 1: تحويل الأس إلى النظام الثنائي
100 = 64 + 32 + 4 = 2⁶ + 2⁵ + 2²
إذن 2¹⁰⁰ = 2⁶⁴ × 2³² × 2⁴

الخطوة 2: حساب قوى 2 بالتربيع المتكرر
2¹ mod 13 = 2
2² mod 13 = 4
2⁴ mod 13 = 16 mod 13 = 3
2⁸ mod 13 = 9
2¹⁶ mod 13 = 81 mod 13 = 3
2³² mod 13 = 9
2⁶⁴ mod 13 = 3

الخطوة 3: الضرب النهائي
2¹⁰⁰ mod 13 = (2⁶⁴ × 2³² × 2⁴) mod 13
= (3 × 9 × 3) mod 13
= 81 mod 13 = 3

✅ النتيجة: 2¹⁰⁰ mod 13 = 3
```

### **💡 مثال 17: التطابق والمعادلات**
```
🎯 المسألة: حل المعادلة 3x ≡ 7 (mod 11)

📋 المعطيات:
• المعادلة: 3x ≡ 7 (mod 11)
• المطلوب: قيمة x

🔧 الحل:

الخطوة 1: التحقق من إمكانية الحل
gcd(3, 11) = 1 ✓ (الحل موجود ووحيد)

الخطوة 2: إيجاد المعكوس الضربي لـ 3 في Z₁₁
نحتاج لإيجاد 3⁻¹ mod 11

باستخدام خوارزمية إقليدس الموسعة:
11 = 3 × 3 + 2
3 = 1 × 2 + 1
2 = 2 × 1 + 0

العمل بالاتجاه العكسي:
1 = 3 - 1 × 2 = 3 - 1 × (11 - 3 × 3) = 4 × 3 - 1 × 11
إذن 3⁻¹ ≡ 4 (mod 11)

الخطوة 3: ضرب طرفي المعادلة في المعكوس
x ≡ 4 × 7 ≡ 28 ≡ 6 (mod 11)

✅ النتيجة: x = 6

🔍 التحقق: 3 × 6 = 18 ≡ 7 (mod 11) ✓
```

### **💡 مثال 18: تطبيق في تشفير أفيني**
```
🎯 المسألة: في تشفير أفيني، استخدم المعادلة C ≡ (5P + 3) mod 26 لتشفير الحرف 'H'

📋 المعطيات:
• معادلة التشفير: C ≡ (aP + b) mod 26
• a = 5, b = 3
• الحرف: H (P = 7)

🔧 الحل:

الخطوة 1: التحقق من صحة المعاملات
gcd(5, 26) = 1 ✓ (التشفير قابل للعكس)

الخطوة 2: تطبيق معادلة التشفير
C ≡ (5 × 7 + 3) mod 26
C ≡ (35 + 3) mod 26
C ≡ 38 mod 26
C ≡ 12 mod 26

الخطوة 3: تحويل إلى حرف
12 → M

✅ النتيجة: الحرف 'H' يُشفر إلى 'M'

💡 فك التشفير:
لفك التشفير، نحتاج للمعادلة: P ≡ a⁻¹(C - b) mod 26
حيث 5⁻¹ ≡ 21 (mod 26)
```

### **💡 مثال 19: مسألة تطبيقية معقدة**
```
🎯 المسألة: في نظام تشفير RSA مبسط، إذا كان n = 33, e = 7, d = 3، شفّر الرسالة M = 5

📋 المعطيات:
• المعامل العام: n = 33
• الأس العام: e = 7
• الأس الخاص: d = 3
• الرسالة: M = 5

🔧 الحل:

الخطوة 1: التشفير
C ≡ M^e mod n
C ≡ 5⁷ mod 33

الخطوة 2: حساب 5⁷ mod 33 بالتربيع المتكرر
5¹ mod 33 = 5
5² mod 33 = 25
5⁴ mod 33 = 625 mod 33 = 625 - 18×33 = 625 - 594 = 31
5⁷ = 5⁴ × 5² × 5¹ = 31 × 25 × 5 mod 33

حساب خطوة بخطوة:
31 × 25 = 775 ≡ 775 - 23×33 = 775 - 759 = 16 (mod 33)
16 × 5 = 80 ≡ 80 - 2×33 = 80 - 66 = 14 (mod 33)

✅ النتيجة: C = 14

🔍 التحقق بفك التشفير:
M ≡ C^d mod n = 14³ mod 33
14² = 196 ≡ 196 - 5×33 = 196 - 165 = 31 (mod 33)
14³ = 31 × 14 = 434 ≡ 434 - 13×33 = 434 - 429 = 5 (mod 33) ✓
```

---

## ↔️ **أمثلة المعكوسات**

### **💡 مثال 20: إيجاد جميع المعكوسات الجمعية**
```
🎯 المسألة: أوجد جميع المعكوسات الجمعية في Z₈

🔧 الحل:

الخطوة 1: تطبيق قانون المعكوس الجمعي
المعكوس الجمعي لـ a في Zn هو (n - a) mod n

الخطوة 2: حساب المعكوسات
• المعكوس الجمعي لـ 0: (8 - 0) mod 8 = 0
• المعكوس الجمعي لـ 1: (8 - 1) mod 8 = 7
• المعكوس الجمعي لـ 2: (8 - 2) mod 8 = 6
• المعكوس الجمعي لـ 3: (8 - 3) mod 8 = 5
• المعكوس الجمعي لـ 4: (8 - 4) mod 8 = 4
• المعكوس الجمعي لـ 5: (8 - 5) mod 8 = 3
• المعكوس الجمعي لـ 6: (8 - 6) mod 8 = 2
• المعكوس الجمعي لـ 7: (8 - 7) mod 8 = 1

✅ النتيجة: الأزواج هي:
(0,0), (1,7), (2,6), (3,5), (4,4), (5,3), (6,2), (7,1)

🔍 التحقق: 3 + 5 = 8 ≡ 0 (mod 8) ✓
```

### **💡 مثال 21: إيجاد المعكوسات الضربية في Z₁₁**
```
🎯 المسألة: أوجد جميع المعكوسات الضربية في Z₁₁

🔧 الحل:

الخطوة 1: تحديد العناصر التي لها معكوسات
في Z₁₁، جميع العناصر عدا 0 لها معكوسات ضربية
(لأن 11 عدد أولي، فـ gcd(a, 11) = 1 لكل a ≠ 0)

الخطوة 2: إيجاد المعكوسات بالتجريب أو خوارزمية إقليدس الموسعة

• 1 × 1 ≡ 1 (mod 11) ⟹ 1⁻¹ = 1
• 2 × 6 ≡ 12 ≡ 1 (mod 11) ⟹ 2⁻¹ = 6
• 3 × 4 ≡ 12 ≡ 1 (mod 11) ⟹ 3⁻¹ = 4
• 4 × 3 ≡ 12 ≡ 1 (mod 11) ⟹ 4⁻¹ = 3
• 5 × 9 ≡ 45 ≡ 1 (mod 11) ⟹ 5⁻¹ = 9
• 6 × 2 ≡ 12 ≡ 1 (mod 11) ⟹ 6⁻¹ = 2
• 7 × 8 ≡ 56 ≡ 1 (mod 11) ⟹ 7⁻¹ = 8
• 8 × 7 ≡ 56 ≡ 1 (mod 11) ⟹ 8⁻¹ = 7
• 9 × 5 ≡ 45 ≡ 1 (mod 11) ⟹ 9⁻¹ = 5
• 10 × 10 ≡ 100 ≡ 1 (mod 11) ⟹ 10⁻¹ = 10

✅ النتيجة: الأزواج هي:
(1,1), (2,6), (3,4), (4,3), (5,9), (6,2), (7,8), (8,7), (9,5), (10,10)
```

### **💡 مثال 22: استخدام خوارزمية إقليدس الموسعة**

#### **📐 القوانين المستخدمة**
1. **شرط وجود المعكوس الضربي**: `gcd(a, n) = 1`
2. **خوارزمية إقليدس الموسعة**: `gcd(a, n) = s×a + t×n`
3. **قانون المعكوس**: إذا كان `1 = s×a + t×n`، فإن `a⁻¹ ≡ s (mod n)`
4. **تحويل النتيجة السالبة**: إذا كان `s < 0`، فإن `a⁻¹ ≡ s + n (mod n)`

#### **🔧 طريقة التطبيق**
- **متى نستخدم**: لإيجاد المعكوس الضربي في أي مجموعة Zn
- **الشروط الإجبارية**: gcd(a, n) = 1 (أولية نسبية)
- **خطوات التطبيق**:
  1. التحقق من شرط الوجود باستخدام خوارزمية إقليدس
  2. تطبيق خوارزمية إقليدس الموسعة
  3. استخراج معامل a من المعادلة النهائية
  4. تحويل النتيجة للمجال الموجب إذا لزم الأمر

#### **🔗 مرجع سريع**
- راجع قسم "المعكوسات الضربية" في `quick-reference.md`
- راجع جدول "خوارزمية إيجاد المعكوس الضربي"

```
🎯 المسألة: أوجد المعكوس الضربي لـ 23 في Z₁₀₀ باستخدام خوارزمية إقليدس الموسعة

📋 المعطيات:
• العنصر: a = 23
• المعامل: n = 100

🔧 الحل:

الخطوة 1: التحقق من شرط الوجود
gcd(100, 23) = ?

100 = 4 × 23 + 8
23 = 2 × 8 + 7
8 = 1 × 7 + 1
7 = 7 × 1 + 0

⟹ gcd(100, 23) = 1 ✓ المعكوس موجود

الخطوة 2: تطبيق خوارزمية إقليدس الموسعة
1 = 8 - 1 × 7
1 = 8 - 1 × (23 - 2 × 8) = 3 × 8 - 1 × 23
1 = 3 × (100 - 4 × 23) - 1 × 23 = 3 × 100 - 13 × 23

الخطوة 3: استخراج المعكوس
من المعادلة: 1 = 3 × 100 + (-13) × 23
⟹ t = -13

الخطوة 4: تحويل للمجال الموجب
المعكوس = (-13) mod 100 = 87

✅ النتيجة: 23⁻¹ ≡ 87 (mod 100)

🔍 التحقق: (23 × 87) mod 100 = 2001 mod 100 = 1 ✓
```

### **💡 مثال 23: حالة عدم وجود معكوس ضربي**
```
🎯 المسألة: هل يوجد معكوس ضربي لـ 15 في Z₂₁؟

📋 المعطيات:
• العنصر: a = 15
• المعامل: n = 21

🔧 الحل:

الخطوة 1: التحقق من شرط الوجود
gcd(21, 15) = ?

21 = 1 × 15 + 6
15 = 2 × 6 + 3
6 = 2 × 3 + 0

⟹ gcd(21, 15) = 3 ≠ 1

✅ النتيجة: لا يوجد معكوس ضربي لـ 15 في Z₂₁

🔍 التفسير:
لأن gcd(15, 21) = 3 ≠ 1، فإن 15 و 21 ليسا أوليين نسبياً.
هذا يعني أنه لا يوجد عدد x بحيث (15 × x) mod 21 = 1.
```

### **💡 مثال 24: تطبيق المعكوسات في حل المعادلات**
```
🎯 المسألة: حل نظام المعادلات:
2x + 3y ≡ 1 (mod 7)
4x + 5y ≡ 6 (mod 7)

🔧 الحل:

الخطوة 1: كتابة النظام في صورة مصفوفية
[2  3] [x]   [1]
[4  5] [y] ≡ [6] (mod 7)

الخطوة 2: حساب محدد المصفوفة
det = (2×5 - 3×4) mod 7 = (10 - 12) mod 7 = -2 mod 7 = 5

الخطوة 3: إيجاد معكوس المحدد
نحتاج 5⁻¹ mod 7
5 × 3 = 15 ≡ 1 (mod 7)
إذن 5⁻¹ ≡ 3 (mod 7)

الخطوة 4: حساب المصفوفة العكسية
A⁻¹ ≡ 3 × [ 5  -3] ≡ [1  4] (mod 7)
           [-4   2]   [5  6]

الخطوة 5: حل النظام
[x] ≡ [1  4] [1] ≡ [1×1 + 4×6] ≡ [25] ≡ [4] (mod 7)
[y]   [5  6] [6]   [5×1 + 6×6]   [41]   [6]

✅ النتيجة: x ≡ 4 (mod 7), y ≡ 6 (mod 7)

🔍 التحقق:
2×4 + 3×6 = 8 + 18 = 26 ≡ 5 ≢ 1 (mod 7)

⚠️ خطأ في الحساب! دعني أعيد الحساب...

الحساب الصحيح:
A⁻¹ ≡ 3 × [ 5  -3] ≡ [15  -9] ≡ [1  5] (mod 7)
           [-4   2]   [-12  6]   [2  6]

[x] ≡ [1  5] [1] ≡ [1×1 + 5×6] ≡ [31] ≡ [3] (mod 7)
[y]   [2  6] [6]   [2×1 + 6×6]   [38]   [3]

✅ النتيجة المصححة: x ≡ 3 (mod 7), y ≡ 3 (mod 7)

🔍 التحقق:
2×3 + 3×3 = 6 + 9 = 15 ≡ 1 (mod 7) ✓
4×3 + 5×3 = 12 + 15 = 27 ≡ 6 (mod 7) ✓
```

---

## 📝 **ملاحظات ختامية**

### **🎯 نصائح لحل المسائل**
1. **اقرأ المسألة بعناية** وحدد نوع المطلوب
2. **اكتب المعطيات والمطلوب** بوضوح
3. **اختر الطريقة المناسبة** للحل
4. **تحقق من النتيجة** دائماً
5. **تدرب على أنواع مختلفة** من المسائل

### **⚠️ أخطاء شائعة يجب تجنبها**
- عدم التحقق من شروط وجود المعكوسات
- الخطأ في التعامل مع الأعداد السالبة في mod
- عدم تبسيط النتائج إلى المجال المطلوب
- الخلط بين العمليات في Z و Zn

### **🔗 الملفات ذات الصلة**
- `summary.md`: التلخيص النظري الشامل
- `quick-reference.md`: الجداول والمراجع السريعة
- `exercises.md`: تمارين إضافية للتدريب

---

## 📖 **دليل استخدام الأقسام المحسنة**

### **🎯 كيفية الاستفادة من التحسينات الجديدة**

#### **📐 قسم "القوانين المستخدمة"**
- **الهدف**: فهم الأساس النظري لكل مثال
- **كيفية الاستخدام**: اقرأ هذا القسم قبل محاولة الحل
- **الفائدة**: يساعدك على تذكر القوانين المناسبة لكل نوع من المسائل

#### **🔧 قسم "طريقة التطبيق"**
- **الهدف**: معرفة متى وكيف تطبق كل قانون
- **كيفية الاستخدام**: راجع الشروط والخطوات قبل البدء
- **الفائدة**: يمنع الأخطاء الشائعة ويوضح الاستراتيجية الصحيحة

#### **🔗 قسم "مرجع سريع"**
- **الهدف**: ربط المثال بالمراجع الشاملة
- **كيفية الاستخدام**: استخدمه للمراجعة السريعة والتوسع
- **الفائدة**: يوفر مصادر إضافية للفهم العميق

### **💡 نصائح للاستخدام الأمثل**
1. **اقرأ الأقسام الثلاثة** قبل محاولة حل المثال
2. **ارجع للمراجع المذكورة** في `quick-reference.md` عند الحاجة
3. **تدرب على تطبيق القوانين** في مسائل مشابهة
4. **استخدم الشروط المذكورة** للتحقق من صحة تطبيقك

### **� التكامل مع الملفات الأخرى**
- **مع summary.md**: للفهم النظري العميق
- **مع quick-reference.md**: للمراجعة السريعة والجداول
- **مع exercises.md**: للتدريب على تطبيق ما تعلمته

---

**�📚 المصدر**: المحاضرة الثانية في التشفير - جامعة الرازي
**👨‍🏫 المدرس**: Asst. Professor M. E. Hodeish
**📅 تاريخ الإعداد**: حسب طريقة CICS المطورة
**🔄 آخر تحديث**: تحسين الأمثلة بالأقسام التفاعلية

---

## 🔗 **روابط سياقية مفيدة**

### **للمراجعة النظرية**
- **مفاهيم التشفير**: راجع [الوحدة الأولى](./summary.md#الوحدة-الأولى-نظرة-عامة-على-التشفير)
- **الحساب الصحيح**: راجع [الوحدة الثانية](./summary.md#الوحدة-الثانية-الحساب-الصحيح)
- **خوارزمية إقليدس**: راجع [الوحدة الثالثة](./summary.md#الوحدة-الثالثة-القاسم-المشترك-الأكبر)
- **الحساب النمطي**: راجع [الوحدة الرابعة](./summary.md#الوحدة-الرابعة-الحساب-النمطي)
- **المعكوسات**: راجع [الوحدة الخامسة](./summary.md#الوحدة-الخامسة-المعكوسات)

### **للمراجعة السريعة**
- **جداول العمليات**: راجع [جداول Zn](./quick-reference.md#جداول-العمليات-النمطية)
- **جداول المعكوسات**: راجع [جداول المعكوسات](./quick-reference.md#جداول-المعكوسات)
- **خوارزميات سريعة**: راجع [الخوارزميات المهمة](./quick-reference.md#خوارزميات-مهمة)

### **للتدريب المتقدم**
- **تمارين مشابهة**: جرب [التمارين المتوسطة](./exercises.md#تمارين-متوسطة---التطبيقات)
- **مسائل متقدمة**: تحدى نفسك مع [التمارين المتقدمة](./exercises.md#تمارين-متقدمة---حل-المسائل)
- **اختبار شامل**: راجع [الأسئلة الشاملة](./exercises.md#أسئلة-مراجعة-شاملة)

---

## ⬅️➡️ **أزرار التنقل**

| ⬅️ **السابق** | 🏠 **الرئيسية** | ➡️ **التالي** |
|:---:|:---:|:---:|
| [التلخيص النظري](./summary.md) | [index.md](./index.md) | [التمارين](./exercises.md) |
| *الفهم النظري* | *جميع الملفات* | *التدريب العملي* |
