Cryptography
CS & N- III
Lecturer:
Asst. Professor: <PERSON><PERSON> <PERSON><PERSON>
L02Cryptography
Lec.:2
An Overview of Cryptography
Mathematics of Cryptography
AL-Razi University
L02
2
Asst. Professor: <PERSON><PERSON> <PERSON><PERSON> Overview of Cryptography
 The general Idea of Cryptographic System:
AL-Razi University
L02
3
Asst. Professor: <PERSON><PERSON> <PERSON><PERSON> Overview of Cryptography
 The General Idea of Cryptographic System:
Hello
Encryption
Algorithm
Zjttp
Zjttp
Decryption
Algorithm
Hello
Receiver
Sender
 Plaintext: Hello
/
AL-Razi University
 Ciphertext: Zjttp
L02
4
Asst. Professor: <PERSON><PERSON> <PERSON><PERSON> Overview of Cryptography
 Basic Terminology
Cryptography It is the science and art of creating secret codes.
.‫السرية‬ ‫الشيفرات‬ ‫إنشاء‬ ‫وفن‬ ‫علم‬ ‫هو‬
Plaintext
It is the original message/data to be protected during
transmission.
‫عملية‬ ‫اء‬‫أثن‬ ‫حمايتها‬ ‫يتطلب‬ ‫والتي‬ ‫األصلية‬ ‫الرسالة‬/‫البيانات‬ ‫به‬ ‫ويقصد‬
.‫النقل‬
AL-Razi University
‫التشفير‬ ‫علم‬
L02
‫األصلي‬ ‫النص‬
5
Asst. Professor: <PERSON><PERSON> <PERSON><PERSON> Overview of Cryptography
 Basic Terminology
Ciphertext
It is the unreadable version of the plaintext.
.‫األصلي‬ ‫النص‬ ‫من‬ ‫للقراءة‬ ‫قابلة‬‫الغير‬ ‫النسخة‬ ‫هو‬
Encryption
It is a cryptographic algorithm and it
consist of
mathematical processes that takes Plaintext and a secret
key as input and outputs a Ciphertext.
‫التها‬‫مدخ‬ ،‫رياضية‬ ‫عمليات‬ ‫عن‬ ‫عبارة‬ ‫والتي‬ ‫تشفير‬ ‫خوارزمية‬ ‫وهي‬
.‫املشفر‬ ‫النص‬ ‫ومخرجاتها‬ ،‫السري‬ ‫واملفتاح‬ ‫األصلي‬ ‫النص‬
AL-Razi University
‫المشفر‬ ‫النص‬
L02
‫التشفير‬
6
Asst. Professor: M. E. HodeishAn Overview of Cryptography
 Basic Terminology
Decryption
It is a cryptographic algorithm and it
consist of
mathematical processes that takes Ciphertext and a secret
key as input and outputs a unique Plaintext.
‫التها‬‫مدخ‬ ،‫رياضية‬ ‫عمليات‬ ‫عن‬ ‫عبارة‬ ‫والتي‬ ‫تشفير‬ ‫فك‬ ‫خوارزمية‬ ‫وهي‬
.‫السري‬ ‫واملفتاح‬ ‫األصلي‬ ‫النص‬ ‫ومخرجاتها‬ ، ‫املشفر‬ ‫النص‬
Secret Key
It is a value that is known to the sender and receiver which
used as an input in the algorithm of encryption & decryption.
‫م‬‫تستخد‬ ‫والتي‬ ‫واملستقبل‬ ‫املرسل‬ ‫لدى‬ ‫معروفة‬ ‫قيمة‬ ‫عن‬ ‫عبارة‬
.‫التشفير‬ ‫وفك‬ ‫التشفير‬ ‫لخوارزميات‬ ‫دخل‬‫كم‬
AL-Razi University
‫التشفير‬ ‫فك‬
L02
‫السري‬ ‫المفتاح‬
7
Asst. Professor: M. E. HodeishAn Overview of Cryptography
 Basic Terminology
It is a collection of algorithms and associated procedures
Cryptosystem
for hiding and revealing (un-hiding) information.
‫إخفاء‬ ‫لغرض‬ ‫بها‬ ‫املرتبطة‬ ‫واإلجراءات‬ ‫الخوارزميات‬ ‫من‬ ‫مجموعة‬
.‫املعلومات‬ ‫وكشف‬
‫التشفير‬ ‫نظام‬
It is the science and art of breaking the created secret
Cryptanalysis
codes.
.‫ا‬‫مسبق‬ ‫املنشأة‬ ‫السرية‬ ‫الشيفرات‬ ‫كسر‬ ‫وفن‬ ‫علم‬ ‫هو‬
AL-Razi University
L02
‫الشفرة‬ ‫تحليل‬
8
Asst. Professor: M. E. HodeishAn Overview of Cryptography
 Basic Terminology
Cryptology
AL-Razi University
Cryptography and Cryptanalysis are called Cryptology.
L02
9
Asst. Professor: M. E. HodeishAn Overview of Cryptography
 Components of Cryptographic System
1) Plaintext.
2) Encryption Algorithm.
3) Ciphertext.
4) Decryption Algorithm.
5) Secret Key.
AL-Razi University
L02
10
Asst. Professor: M. E. HodeishAn Overview of Cryptography
 Cryptography Classifications/Types
 Regarding to the using of Computer System, cryptography can
broadly be classified into two classifications:
Cryptography
Classic CryptographyModern Cryptography
 Implementing the Algorithm
without Computer. Implementing the Algorithm
with Computer.
AL-Razi University
L02
11
Asst. Professor: M. E. HodeishAn Overview of Cryptography
Cryptography
 Cryptography Classifications/Types: based on the number of secret keys used
Symmetric Key
CryptographyUses a single secret key for both
encryption and decryption.
Asymmetric key
CryptographyUses two secret keys: public key for
encryption and private key for
decryption.
Hashing
AL-Razi University
Keyless function.
L02
12
Asst. Professor: M. E. Hodeish‫يستخدم‬‫مفتاحني‬ ‫سريني‬ ‫‪:‬‬‫املف‬ ‫تاح‬‫العام‬ ‫للتشفري‬
‫التشفير‬‫الغير‬ ‫واملفتاح‬‫اخلاص‬ ‫لفك‬ ‫التشفري‬
‫متناظر‬‫‪/‬‬‫متماثل‬
‫المزج‬‫دالة‬‫اهلاش‬ ‫ال‬ ‫تستخدم‬ ‫أي‬ ‫مفتاح‬
‫‪Hodeish‬‬‫‪E.‬‬ ‫‪M.‬‬ ‫‪Professor:‬‬ ‫‪Asst.‬‬
‫‪L02‬‬
‫‪Cryptography‬‬
‫‪13‬‬
‫التشفير‬‫يستخدم‬‫مفتاح‬ ‫سري‬ ‫واحد‬ ‫للتشف‬ ‫ري‬‫وفك‬ ‫التشفري‬
‫المتناظر‬‫‪/‬‬‫المتماثل‬Symmetric Key Cryptography
Mathematics of Cryptography
AL-Razi University
L02
14
Asst. Professor: M. E. HodeishMathematics of Cryptography
 Objectives
 To review integer arithmetic, concentrating on divisibility
and finding the greatest common divisor using the Euclidean
algorithm.
 To understand how the extended Euclidean algorithm can be
used to solve linear Diophantine equations, to solve linear
congruent equations, and to find the multiplicative inverses.
 To emphasize the importance of modular arithmetic and the
modulo operator, because they are extensively used in
cryptography.
AL-Razi University
L02
15
Asst. Professor: M. E. HodeishMathematics of Cryptography
2-1 INTEGER ARITHMETIC
In integer arithmetic, we use a set and a few operations. You are
familiar with this set and the corresponding operations, but they
are reviewed here to create a background for modular arithmetic.
Topics discussed in this section:
2.1.1 Set of Integers
2.1.2 Binary Operations
2.1.3 Intègre Division
2.1.4 Divisibility
AL-Razi University
L02
16
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.1 Set of Integers
The set of integers, denoted by Z, contains all integral numbers
(with no fraction) from negative infinity to positive infinity
(Figure 2.1).
Figure 2.1 The set of integers
AL-Razi University
L02
17
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.2 Binary Operations
 In cryptography, we are interested in three binary operations
applied to the set of integers.
 A binary operation takes two inputs and creates one output.
Figure 2.2 Three binary operations for the set of integers
AL-Razi University
L02
18
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.2 Binary Operations Continued
Example 2.1
 The following shows the results of the three binary operations on
two integers.
 Because each input can be either positive or negative, we can have
four cases for each operation.
AL-Razi University
L02
19
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.3 Integer Division
 In integer arithmetic, if we divide a by n, we can get q And r.
 The relationship between these four integers can be shown as:
a=q×n+r
AL-Razi University
L02
20
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.3 Integer Division Continued
 Assume that a = 255 and n = 11,
Example 2.2
 We can find q = 23 and R = 2 using the division algorithm.
Figure 2.3
Example 2.2, finding the
quotient and the remainder
AL-Razi University
L02
21
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.3 Integer Division Continued
Figure 2.4 Division algorithm for integers
AL-Razi University
L02
22
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.3 Integer Division Continued
Example 2.3
 When we use a computer or a calculator,
r and q are negative when a is negative.
 How can we apply the restriction that r needs to be positive?
 The solution is simple, we decrement the value of q by 1,
 and we add the value of n to r to make it positive.
AL-Razi University
L02
23
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility
If a is not zero and we let r = 0 in the division relation, we get
a=q×n
If the remainder is zero,
If the remainder is not zero,
AL-Razi University
L02
24
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Continued
Example 2.4
a. The integer 4 divides the integer 32 because 32 = 8 × 4.
We show this as
b. The number 8 does not divide the number 42 because
42 = 5 × 8 + 2. There is a remainder, the number 2, in the
equation. We show this as
AL-Razi University
L02
25
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Continued
Properties
Property 1: if a|1, then a = ±1.
Property 2: if a|b and b|a, then a = ±b.
Property 3: if a|b and b|c, then a|c.
Property 4: if a|b and a|c, then
a|(m × b + n × c), where m
and n are arbitrary integers
AL-Razi University
L02
26
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Continued
Example 2.5
AL-Razi University
L02
27
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisbility Continued
Example 2.6
AL-Razi University
L02
28
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Continued
Note
Fact 1: The integer 1 has only one
divisor, itself.
Fact 2: Any positive integer has at least
two divisors, 1 and itself
(but it can have more).
AL-Razi University
L02
29
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Greatest Common Divisor GCD
 Two positive integers may have many common divisors,
 But, only one Greatest Common Divisor
Figure 2.6 Common divisors of two integers
AL-Razi University
L02
30
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Greatest Common Divisor GCD
Note
Greatest Common Divisor
The greatest common divisor of two positive integers is the
largest integer that can divide both integers.
Note
Euclidean Algorithm
Fact 1: gcd (a, 0) = a
Fact 2: gcd (a, b) = gcd (b, r), where r is
the remainder of dividing a by b
AL-Razi University
L02
31
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Greatest Common Divisor GCD
Figure 2.7 Euclidean Algorithm
AL-Razi University
L02
32
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility Greatest Common Divisor GCD
Note
When gcd (a, b) = 1, we say that a and b
are relatively prime.
AL-Razi University
L02
33
Asst. Professor: M. E. HodeishMathematics of Cryptography
Example 2.7
Find
the
greatest
common divisor of
2740 and 1760.
Solution
We have
gcd (2740, 1760) = 20.
AL-Razi University
L02
34
Asst. Professor: M. E. HodeishMathematics of Cryptography
Example 2.8
Find the greatest
common divisor of 25
and 60.
Solution
We have
gcd (25, 65) = 5.
AL-Razi University
L02
35
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
 The extended Euclidean algorithm can calculate the gcd (a, b)
and at the same time calculate the value of s and t.
 Given two integers a and b, we often need to find other two
integers, s and t, such that
AL-Razi University
L02
36
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Figure 2.8.a Extended Euclidean algorithm, part a
AL-Razi University
L02
37
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Figure 2.8.b Extended Euclidean algorithm, part b
AL-Razi University
L02
38
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Figure 2.8.b Extended Euclidean algorithm, part b
AL-Razi University
L02
39
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Figure 2.8.b Extended Euclidean algorithm, part b
AL-Razi University
L02
40
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Example 2.9
Given a = 161 and b = 28, find gcd (a, b) and the values of s and t.
We get gcd (161, 28) = 7, s = −1 and t = 6.
Solution
AL-Razi University
L02
41
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Example 2.10
Given a = 17 and b = 0, find gcd (a, b) and the values of s
and t.
Solution
We get gcd (17, 0) = 17, s = 1, and t = 0.
AL-Razi University
L02
42
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.1.4 Divisibility GCD : Extended Euclidean Algorithm
Example 2.11
Given a = 0 and b = 45, find gcd (a, b) and the values of s
and t.
Solution
We get gcd (0, 45) = 45, s = 0, and t = 1.
AL-Razi University
L02
43
Asst. Professor: M. E. HodeishMathematics of Cryptography
2-2 MODULAR ARITHMETIC
 The division relationship (a = q × n + r) discussed in the previous
section has two inputs (a and n) and two outputs (q and r).
 In modular arithmetic, we are interested in only one of the outputs,
the remainder r.
Topics discussed in this section:
2.2.1 Modular Operator
2.2.2 Set of Residues
2.2.3 Congruence
2.2.4 Operations in Zn
2.2.5 Addition and Multiplication Tables
AL-Razi University
L02
44
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.1 Modulo Operator
 The modulo operator is shown as mod.
 The second input (n) is called the modulus.
 The output r is called the residue.
Figure 2.9 Division algorithm and modulo operator
AL-Razi University
L02
45
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.1 Modulo Operator Continued
Example 2.14
Find the result of the following operations:
a. 27 mod 5
b. 36 mod 12
c. −18 mod 14
d. −7 mod 10
Solution
a. Dividing 27 by 5 results in r = 2
b. Dividing 36 by 12 results in r = 0.
c. Dividing −18 by 14 results in r = −4. After adding the modulus r = 10
This means that −18 mod 14 = 10.
d. Dividing −7 by 10 results in r = −7. After adding the modulus to −7, r = 3.
AL-Razi University
L02
46
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.2 Set of Residues
 The modulo operation creates a set, which in modular arithmetic
is referred to as the set of least residues modulo n, or Zn.
Figure 2.10 Some Zn sets
AL-Razi University
L02
47
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.3 Congruence
 To show that two integers are congruent, we use the
congruence operator ( ≡ ).
 For example, The result of :
2 mod 10 = 2,
12 mod 10 = 2,
22 mod 10 = 2, and so on
 In modular arithmetic,
integers like 2, 12, and 22 are called congruent mod 10.
AL-Razi University
L02
48
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.3 Congruence Continued
Figure 2.11 Concept of congruence
AL-Razi University
L02
49
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.3 Congruence Continued
 Is -8 mod 10 = 2? If Yes, How?
a=q×n+r
-8 = 0 × 10 + -8
 Now decrement the value of q by 1, and add the value of n to r
-8 = (0 - 1) × 10 + (-8 + 10)
-8 = -1 × 10 + 2
AL-Razi University
L02
50
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.4 Operation in Zn
The three binary operations that we discussed for the set Z can also be
defined for the set Zn. The result may need to be mapped to Zn using the
mod operator.
Figure 2.13 Binary operations in Zn
AL-Razi University
L02
51
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.4 Operation in Zn Continued
Perform the following operations (the inputs come from Zn):
Example 2.16 a. Add 7 to 14 in Z15.
b. Subtract 11 from 7 in Z13.
c. Multiply 11 by 7 in Z20.
Solution
AL-Razi University
L02
52
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.4 Operation in Zn Continued
Properties
AL-Razi University
L02
53
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.4 Operation in Zn Continued
Properties of mode operator
AL-Razi University
L02
54
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.4 Operation in Zn Continued
Properties of mode operator
Example 2.18
The following shows the application of the above properties:
1. (1,723,345 + 2,124,945) mod 11 = (8 + 9) mod 11 = 6
2. (1,723,345 − 2,124,945) mod 16 = (8 − 9) mod 11 = 10
3. (1,723,345 × 2,124,945) mod 16 = (8 × 9) mod 11 = 6
AL-Razi University
L02
55
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.4 Operation in Zn Continued
Properties of mode operator
 In arithmetic, we often need to find the remainder of powers of
10 when divided by an integer.
We have:
AL-Razi University
L02
56
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses
 When we are working in modular arithmetic, we often
need to find the inverse of a number relative to an
operation.

We are normally looking for an additive inverse
(relative to an addition operation) or,
 A multiplicative inverse (relative to a multiplication
operation).
AL-Razi University
L02
57
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Additive Inverse
 In Zn, two numbers a and b are additive inverses of each
other if:
Note
 In modular arithmetic, each integer has an additive inverse.
 The sum of an integer and its additive inverse is congruent to 0
modulo n.
 Each number has one and only one additive inverse.
 However, the inverse of the number may be the number itself.
AL-Razi University
L02
58
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Additive Inverse
Example 2.21 Find all additive inverse pairs in Z10.
Solution
The six pairs of additive inverses are:
(0, 0), (1, 9), (2, 8), (3, 7), (4, 6), and (5, 5).
Note
 In this list, 0 is the additive inverse of itself; so is 5.
 Note that the additive inverses are reciprocal;
 if 4 is the additive inverse of 6, then 6 is also the additive
inverse of 4.
AL-Razi University
L02
59
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
 In Zn, two numbers a and b are the multiplicative
inverse of each other if
 For example,
 If the modulus is 10, then the multiplicative inverse of 3
is 7.
 In other words, we have (3 × 7) mod 10 = 1.
AL-Razi University
L02
60
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
Note
 In modular arithmetic, an integer may or may not have a
multiplicative inverse.
 When it does, the product of the integer and its multiplicative
inverse is congruent to 1 modulo n.
AL-Razi University
L02
61
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
Example 2.22
Find the multiplicative inverse of 8 in Z10.
Solution
 There is no multiplicative inverse because gcd (10, 8) = 2 ≠ 1.
 In other words, we cannot find any number between 0 and 9
such that when multiplied by 8, the result is congruent to 1.
AL-Razi University
L02
62
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
Example 2.23
Find all multiplicative inverses in Z10.
Solution
 There are only three pairs: (1, 1), (3, 7) and (9, 9).
 The numbers 0, 2, 4, 5, 6, and 8 do not have a multiplicative
inverse.
AL-Razi University
L02
63
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
Example 2.24
Find all multiplicative inverse pairs in Z11.
Solution
 We have seven pairs: (1, 1), (2, 6), (3, 4), (5, 9), (7, 8), (9, 9), and
(10, 10).
AL-Razi University
L02
64
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
 The extended Euclidean algorithm
 The extended Euclidean algorithm finds the multiplicative
inverses of b in Zn when n and b are given and
 gcd (n, b) = 1.
 The multiplicative inverse of b is the value of t after being
mapped to Zn.
AL-Razi University
L02
65
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse
 The extended Euclidean algorithm
AL-Razi University
L02
66
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse  The extended Euclidean algorithm
Example 2.25 Find the multiplicative inverse of 11 in Z26.
Solution
The gcd (26, 11) is 1; the inverse of 11 is -7 or 19.
AL-Razi University
L02
67
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse  The extended Euclidean algorithm
Example 2.25 Find the multiplicative inverse of 11 in Z26.
Solution
 The gcd (26, 11) is 1, which means that the multiplicative
inverse of 11 exists.
 The extended Euclidean algorithm gives t1 = −7.
 The multiplicative inverse is (−7) mod 26 = 19.
 In other words, 11 and 19 are multiplicative inverse in Z26.
 We can see that (11 × 19) mod 26 = 209 mod 26 = 1.
AL-Razi University
L02
68
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.5 Inverses: Multiplicative Inverse  The extended Euclidean algorithm
Example 2.25 Find the inverse of 12 in Z26.
Solution
The gcd (26, 12) is 2; the inverse does not exist.
AL-Razi University
L02
69
Asst. Professor: M. E. HodeishMathematics of Cryptography
2.2.6 Addition and Multiplication Tables
AL-Razi University
L02
70
Asst. Professor: M. E. Hodeish
