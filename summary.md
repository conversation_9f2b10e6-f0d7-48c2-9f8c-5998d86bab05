# 📚 **ملف التلخيص الرئيسي - المحاضرة الثانية في التشفير**
## **تطبيق طريقة CICS (Comprehensive Integrated Cryptography Summarization)**

---

## 🧭 **قائمة التنقل**

| 🏠 [الفهرس الرئيسي](./index.md) | 💡 [الأمثلة العملية](./examples.md) | 📊 [المراجع السريعة](./quick-reference.md) | 📝 [التمارين](./exercises.md) |
|:---:|:---:|:---:|:---:|
| **البداية** | **التطبيق** | **المرجع** | **التدريب** |

### **🎯 أنت هنا: التلخيص النظري الشامل**
- **الهدف**: فهم المفاهيم النظرية الأساسية
- **المحتوى**: 5 وحدات رئيسية مع تعريفات وقوانين
- **الوقت المقترح**: 45-60 دقيقة للقراءة الأولى
- **التالي المقترح**: [الأمثلة العملية](./examples.md) للتطبيق

---

## 🎯 **معلومات المحاضرة**
- **العنوان**: نظرة عامة على التشفير ورياضيات التشفير
- **الرقم**: المحاضرة الثانية (L02)
- **المدرس**: Asst. Professor M. E. Hodeish
- **الجامعة**: AL-Razi University

---

## 📋 **فهرس المحتويات**

| القسم | الموضوع | الصفحة |
|-------|---------|--------|
| 🔐 | [الوحدة الأولى: نظرة عامة على التشفير](#الوحدة-الأولى-نظرة-عامة-على-التشفير) | 3 |
| 🧮 | [الوحدة الثانية: الحساب الصحيح](#الوحدة-الثانية-الحساب-الصحيح) | 8 |
| 🔍 | [الوحدة الثالثة: القاسم المشترك الأكبر](#الوحدة-الثالثة-القاسم-المشترك-الأكبر) | 12 |
| 🔄 | [الوحدة الرابعة: الحساب النمطي](#الوحدة-الرابعة-الحساب-النمطي) | 16 |
| ↔️ | [الوحدة الخامسة: المعكوسات](#الوحدة-الخامسة-المعكوسات) | 20 |

---

## 🎨 **نظام الترميز المستخدم**

| الرمز | المعنى | الاستخدام |
|-------|---------|-----------|
| 🔐 | مفهوم أساسي | التعريفات الرئيسية |
| 🧮 | عملية رياضية | القوانين والخوارزميات |
| 💡 | مثال عملي | التطبيقات والأمثلة |
| ⚠️ | تحذير/ملاحظة مهمة | النقاط الحرجة |
| 🔗 | ربط بين المفاهيم | العلاقات والاتصالات |
| 📊 | جدول/إحصائية | البيانات المنظمة |
| 🎯 | هدف تعليمي | الأهداف والنتائج |

---

## 🔐 **الوحدة الأولى: نظرة عامة على التشفير**

### **🎯 الأهداف التعليمية**
- فهم المفاهيم الأساسية للتشفير
- التمييز بين أنواع التشفير المختلفة
- معرفة مكونات نظام التشفير

### **📖 المفاهيم الأساسية**

#### **🔐 التشفير (Cryptography)**
```
🔐 التشفير
├── التعريف: علم وفن إنشاء الشيفرات السرية
├── الهدف: حماية المعلومات أثناء النقل
├── المكونات: خوارزمية + مفتاح + نص
└── النتيجة: تحويل النص الواضح إلى نص مشفر
```

#### **🔐 النص الأصلي (Plaintext)**
```
🔐 النص الأصلي
├── التعريف: الرسالة/البيانات الأصلية المراد حمايتها
├── الرمز: P أو M
├── الخصائص: قابل للقراءة والفهم
└── المثال: "Hello" → نص واضح
```

#### **🔐 النص المشفر (Ciphertext)**
```
🔐 النص المشفر
├── التعريف: النسخة غير القابلة للقراءة من النص الأصلي
├── الرمز: C
├── الخصائص: غير مفهوم بدون فك التشفير
└── المثال: "Zjttp" → نص مشفر
```

#### **🔐 المفتاح السري (Secret Key)**
```
🔐 المفتاح السري
├── التعريف: قيمة معروفة للمرسل والمستقبل
├── الرمز: K
├── الاستخدام: مدخل لخوارزميات التشفير وفك التشفير
└── الأهمية: أساس أمان النظام
```

### **🧮 العمليات الأساسية**

#### **📐 عملية التشفير**
```
Encryption: E(P, K) = C
حيث:
• E: خوارزمية التشفير
• P: النص الأصلي
• K: المفتاح السري
• C: النص المشفر
```

#### **📐 عملية فك التشفير**
```
Decryption: D(C, K) = P
حيث:
• D: خوارزمية فك التشفير
• C: النص المشفر
• K: المفتاح السري
• P: النص الأصلي المستعاد
```

### **📊 تصنيفات التشفير**

#### **🔗 التصنيف الأول: حسب استخدام الحاسوب**
| النوع | الوصف | الأمثلة |
|-------|--------|---------|
| **التشفير الكلاسيكي** | تنفيذ بدون حاسوب | قيصر، فيجينير |
| **التشفير الحديث** | تنفيذ بالحاسوب | AES، RSA |

#### **🔗 التصنيف الثاني: حسب عدد المفاتيح**

##### **🔐 التشفير المتماثل (Symmetric)**
```
🔐 التشفير المتماثل
├── المفاتيح: مفتاح واحد للتشفير وفك التشفير
├── المميزات: سرعة عالية، بساطة التنفيذ
├── العيوب: صعوبة توزيع المفاتيح
└── الأمثلة: DES، AES،3DES
```

##### **🔐 التشفير غير المتماثل (Asymmetric)**
```
🔐 التشفير غير المتماثل
├── المفاتيح: مفتاحان (عام وخاص)
├── المميزات: سهولة توزيع المفاتيح، التوقيع الرقمي
├── العيوب: بطء نسبي، تعقيد أكبر
└── الأمثلة: RSA، ECC، ElGamal
```

##### **🔐 دالة الهاش (Hashing)**
```
🔐 دالة الهاش
├── المفاتيح: لا تستخدم مفاتيح
├── الهدف: التحقق من سلامة البيانات
├── الخصائص: اتجاه واحد، حجم ثابت للمخرجات
└── الأمثلة: SHA-256، MD5، SHA-3
```

### **🏗️ مكونات نظام التشفير**
1. **النص الأصلي (Plaintext)**
2. **خوارزمية التشفير (Encryption Algorithm)**
3. **النص المشفر (Ciphertext)**
4. **خوارزمية فك التشفير (Decryption Algorithm)**
5. **المفتاح السري (Secret Key)**

### **💡 مثال توضيحي: نظام التشفير الأساسي**
```
🎯 السيناريو: إرسال رسالة سرية

📋 المعطيات:
• المرسل: Alice
• المستقبل: Bob
• الرسالة: "HELLO"
• طريقة التشفير: إزاحة قيصر
• المفتاح: إزاحة بـ 5 مواضع

🔧 العملية:
الخطوة 1: Alice تشفر الرسالة
H → M, E → J, L → Q, L → Q, O → T
النتيجة: "MJQQT"

الخطوة 2: إرسال النص المشفر عبر القناة
"MJQQT" → القناة غير الآمنة → "MJQQT"

الخطوة 3: Bob يفك التشفير
M → H, J → E, Q → L, Q → L, T → O
النتيجة: "HELLO"

✅ النتيجة: تم نقل الرسالة بأمان
```

### **⚠️ ملاحظات مهمة**
- **أمان النظام** يعتمد على سرية المفتاح وليس سرية الخوارزمية
- **التشفير الكلاسيكي** سهل الكسر بالطرق الحديثة
- **التشفير الحديث** يعتمد على مسائل رياضية صعبة الحل

---

## 🧮 **الوحدة الثانية: الحساب الصحيح**

### **🎯 الأهداف التعليمية**
- فهم العمليات الأساسية على الأعداد الصحيحة
- إتقان عملية القسمة مع الباقي
- فهم مفهوم القابلية للقسمة وخصائصها

### **📖 المفاهيم الأساسية**

#### **🔐 مجموعة الأعداد الصحيحة (Set of Integers)**
```
🔐 مجموعة الأعداد الصحيحة
├── الرمز: Z
├── التعريف: جميع الأعداد الصحيحة من -∞ إلى +∞
├── العناصر: {..., -3, -2, -1, 0, 1, 2, 3, ...}
└── الخصائص: مغلقة تحت الجمع والطرح والضرب
```

#### **🔐 العمليات الثنائية (Binary Operations)**
```
🔐 العمليات الثنائية
├── الجمع (+): a + b = c
├── الطرح (-): a - b = c
├── الضرب (×): a × b = c
└── الخاصية: تأخذ مدخلين وتنتج مخرج واحد
```

### **🧮 القسمة الصحيحة (Integer Division)**

#### **📐 القانون الأساسي**
```
a = q × n + r

حيث:
• a: المقسوم (dividend)
• q: خارج القسمة (quotient)
• n: المقسوم عليه (divisor)
• r: الباقي (remainder)
• الشرط: 0 ≤ r < |n|
```

#### **💡 مثال: القسمة العادية**
```
🎯 المسألة: قسم 255 على 11

📋 الحل:
255 ÷ 11 = 23 والباقي 2

🔍 التحقق:
255 = 23 × 11 + 2
255 = 253 + 2 = 255 ✓
```

#### **💡 مثال: القسمة مع عدد سالب**
```
🎯 المسألة: قسم -18 على 14

📋 الحل الأولي:
-18 ÷ 14 = -1 والباقي -4
تحقق: -18 = (-1) × 14 + (-4) = -14 - 4 = -18 ✓

⚠️ مشكلة: الباقي سالب!

🔧 التصحيح:
q = q - 1 = -1 - 1 = -2
r = r + n = -4 + 14 = 10

🔍 التحقق النهائي:
-18 = (-2) × 14 + 10 = -28 + 10 = -18 ✓
والآن: 0 ≤ 10 < 14 ✓

✅ النتيجة: -18 = (-2) × 14 + 10
```

### **🧮 القابلية للقسمة (Divisibility)**

#### **🔐 التعريف**
```
🔐 القابلية للقسمة
├── التعريف: n يقسم a إذا كان a = q × n (الباقي = 0)
├── الرمز: n | a (n يقسم a)
├── الرمز: n ∤ a (n لا يقسم a)
└── المثال: 4 | 32 لأن 32 = 8 × 4
```

#### **📊 خصائص القابلية للقسمة**
| الخاصية | الصيغة الرياضية | المثال |
|---------|-----------------|---------|
| **الخاصية 1** | إذا كان a\|1، فإن a = ±1 | 1\|1، (-1)\|1 |
| **الخاصية 2** | إذا كان a\|b و b\|a، فإن a = ±b | 6\|12 و 12\|6 ⟹ مستحيل |
| **الخاصية 3** | إذا كان a\|b و b\|c، فإن a\|c | 3\|6 و 6\|18 ⟹ 3\|18 |
| **الخاصية 4** | إذا كان a\|b و a\|c، فإن a\|(mb+nc) | 5\|10 و 5\|15 ⟹ 5\|(2×10+3×15) |

---

## 🔍 **الوحدة الثالثة: القاسم المشترك الأكبر**

### **🎯 الأهداف التعليمية**
- فهم مفهوم القاسم المشترك الأكبر
- إتقان خوارزمية إقليدس
- تطبيق خوارزمية إقليدس الموسعة

### **🔐 القاسم المشترك الأكبر (GCD)**
```
🔐 القاسم المشترك الأكبر
├── التعريف: أكبر عدد صحيح موجب يقسم عددين معطيين
├── الرمز: gcd(a, b)
├── الخاصية: gcd(a, b) = gcd(b, a)
└── الحالة الخاصة: gcd(a, 0) = |a|
```

### **🧮 خوارزمية إقليدس (Euclidean Algorithm)**

#### **📐 القوانين الأساسية**
```
1. gcd(a, 0) = a
2. gcd(a, b) = gcd(b, a mod b)
```

#### **💡 مثال محلول: gcd(2740, 1760)**
```
🎯 المسألة: احسب gcd(2740, 1760)

🔧 الحل خطوة بخطوة:

الخطوة 1: gcd(2740, 1760)
2740 = 1 × 1760 + 980
⟹ gcd(2740, 1760) = gcd(1760, 980)

الخطوة 2: gcd(1760, 980)
1760 = 1 × 980 + 780
⟹ gcd(1760, 980) = gcd(980, 780)

الخطوة 3: gcd(980, 780)
980 = 1 × 780 + 200
⟹ gcd(980, 780) = gcd(780, 200)

الخطوة 4: gcd(780, 200)
780 = 3 × 200 + 180
⟹ gcd(780, 200) = gcd(200, 180)

الخطوة 5: gcd(200, 180)
200 = 1 × 180 + 20
⟹ gcd(200, 180) = gcd(180, 20)

الخطوة 6: gcd(180, 20)
180 = 9 × 20 + 0
⟹ gcd(180, 20) = gcd(20, 0) = 20

✅ النتيجة النهائية: gcd(2740, 1760) = 20
```

### **🧮 خوارزمية إقليدس الموسعة**

#### **🔐 الهدف**
```
🔐 خوارزمية إقليدس الموسعة
├── الهدف: إيجاد gcd(a, b) وقيم s, t
├── المعادلة: gcd(a, b) = s × a + t × b
├── الاستخدام: إيجاد المعكوسات الضربية
└── الأهمية: أساسية في خوارزميات التشفير
```

#### **💡 مثال محلول: Extended GCD**
```
🎯 المسألة: احسب gcd(161, 28) وقيم s, t

🔧 الحل:

الخطوة 1: تطبيق خوارزمية إقليدس
161 = 5 × 28 + 21
28 = 1 × 21 + 7
21 = 3 × 7 + 0
⟹ gcd(161, 28) = 7

الخطوة 2: العمل بالاتجاه العكسي
7 = 28 - 1 × 21
7 = 28 - 1 × (161 - 5 × 28)
7 = 28 - 161 + 5 × 28
7 = 6 × 28 - 1 × 161
7 = (-1) × 161 + 6 × 28

✅ النتيجة: gcd(161, 28) = 7, s = -1, t = 6

🔍 التحقق: (-1) × 161 + 6 × 28 = -161 + 168 = 7 ✓
```

---

## 🔄 **الوحدة الرابعة: الحساب النمطي**

### **🎯 الأهداف التعليمية**
- فهم العامل النمطي ومجموعة البواقي
- إتقان العمليات في الحساب النمطي
- تطبيق خصائص العامل النمطي

### **🔐 العامل النمطي (Modulo Operator)**
```
🔐 العامل النمطي
├── الرمز: a mod n
├── التعريف: باقي قسمة a على n
├── الشرط: n > 0, 0 ≤ r < n
└── المثال: 17 mod 5 = 2
```

### **🔐 مجموعة البواقي (Set of Residues)**
```
🔐 مجموعة البواقي Zn
├── التعريف: {0, 1, 2, ..., n-1}
├── الحجم: n عنصر
├── المثال: Z₅ = {0, 1, 2, 3, 4}
└── الاستخدام: أساس العمليات النمطية
```

### **📊 أمثلة على مجموعات البواقي**
| المجموعة | العناصر | الحجم |
|----------|---------|-------|
| Z₃ | {0, 1, 2} | 3 |
| Z₅ | {0, 1, 2, 3, 4} | 5 |
| Z₁₀ | {0, 1, 2, 3, 4, 5, 6, 7, 8, 9} | 10 |
| Z₁₂ | {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11} | 12 |

### **🧮 العمليات في Zn**

#### **📐 خصائص العامل النمطي**
```
1. (a + b) mod n = ((a mod n) + (b mod n)) mod n
2. (a - b) mod n = ((a mod n) - (b mod n)) mod n
3. (a × b) mod n = ((a mod n) × (b mod n)) mod n
4. aᵏ mod n = ((a mod n)ᵏ) mod n
```

#### **💡 مثال: تطبيق خصائص mod**
```
🎯 المسألة: احسب (1,723,345 × 2,124,945) mod 11

📋 الحل باستخدام الخصائص:

الخطوة 1: حساب البواقي
1,723,345 mod 11 = 8
2,124,945 mod 11 = 9

الخطوة 2: تطبيق خاصية الضرب
(1,723,345 × 2,124,945) mod 11 = (8 × 9) mod 11

الخطوة 3: الحساب النهائي
(8 × 9) mod 11 = 72 mod 11 = 6

✅ النتيجة: 6

🔍 الفائدة: تجنب حساب الأرقام الكبيرة جداً
```

### **🔐 التطابق (Congruence)**
```
🔐 التطابق
├── الرمز: a ≡ b (mod n)
├── المعنى: a mod n = b mod n
├── المثال: 17 ≡ 5 ≡ -1 (mod 6)
└── الخاصية: علاقة تكافؤ
```

---

## ↔️ **الوحدة الخامسة: المعكوسات**

### **🎯 الأهداف التعليمية**
- فهم مفهوم المعكوس الجمعي والضربي
- إيجاد المعكوسات في مجموعات البواقي
- استخدام خوارزمية إقليدس الموسعة لإيجاد المعكوسات

### **🔐 المعكوس الجمعي (Additive Inverse)**
```
🔐 المعكوس الجمعي
├── التعريف: a + (-a) ≡ 0 (mod n)
├── الوجود: كل عنصر له معكوس جمعي وحيد
├── الصيغة: المعكوس الجمعي لـ a هو (n - a) mod n
└── المثال: في Z₁₀، المعكوس الجمعي لـ 3 هو 7
```

### **🔐 المعكوس الضربي (Multiplicative Inverse)**
```
🔐 المعكوس الضربي
├── التعريف: a × a⁻¹ ≡ 1 (mod n)
├── شرط الوجود: gcd(a, n) = 1
├── الإيجاد: خوارزمية إقليدس الموسعة
└── المثال: في Z₁₁، المعكوس الضربي لـ 3 هو 4
```

### **📊 جدول المعكوسات في Z₁₀**
| العدد | المعكوس الجمعي | المعكوس الضربي |
|-------|----------------|-----------------|
| 0 | 0 | لا يوجد |
| 1 | 9 | 1 |
| 2 | 8 | لا يوجد |
| 3 | 7 | 7 |
| 4 | 6 | لا يوجد |
| 5 | 5 | لا يوجد |
| 6 | 4 | لا يوجد |
| 7 | 3 | 3 |
| 8 | 2 | لا يوجد |
| 9 | 1 | 9 |

### **💡 مثال محلول: إيجاد المعكوس الضربي**
```
🎯 المسألة: احسب المعكوس الضربي لـ 11 في Z₂₆

📋 الحل:

الخطوة 1: التحقق من شرط الوجود
gcd(26, 11) = ?

26 = 2 × 11 + 4
11 = 2 × 4 + 3
4 = 1 × 3 + 1
3 = 3 × 1 + 0

⟹ gcd(26, 11) = 1 ✓ المعكوس موجود

الخطوة 2: تطبيق خوارزمية إقليدس الموسعة
1 = 4 - 1 × 3
1 = 4 - 1 × (11 - 2 × 4) = 3 × 4 - 1 × 11
1 = 3 × (26 - 2 × 11) - 1 × 11 = 3 × 26 - 7 × 11

الخطوة 3: استخراج المعكوس
من المعادلة: 1 = 3 × 26 + (-7) × 11
⟹ t = -7

الخطوة 4: تحويل للمجال الموجب
المعكوس = (-7) mod 26 = 19

✅ النتيجة: 11⁻¹ ≡ 19 (mod 26)

🔍 التحقق: (11 × 19) mod 26 = 209 mod 26 = 1 ✓
```

---

## 📋 **ملخص سريع للوحدات**

### **🎯 النقاط الرئيسية**
1. **التشفير** يحول النصوص الواضحة إلى نصوص مشفرة باستخدام مفاتيح
2. **الحساب الصحيح** يوفر الأساس الرياضي للعمليات التشفيرية
3. **خوارزمية إقليدس** ضرورية لإيجاد القواسم المشتركة والمعكوسات
4. **الحساب النمطي** أساس جميع خوارزميات التشفير الحديثة
5. **المعكوسات** ضرورية لعمليات فك التشفير

### **🔗 الروابط بين المفاهيم**
```
التشفير → يحتاج → الرياضيات
    ↓           ↓
المفاتيح → تستخدم → الحساب النمطي
    ↓           ↓
فك التشفير → يحتاج → المعكوسات
    ↓           ↓
الأمان → يعتمد على → صعوبة المسائل الرياضية
```

---

## ⚠️ **نصائح للمراجعة**
1. **ابدأ بالمفاهيم الأساسية** قبل الانتقال للتطبيقات
2. **تدرب على الأمثلة** خطوة بخطوة
3. **احفظ الخصائص الرياضية** المهمة
4. **ربط النظرية بالتطبيق** في التشفير
5. **استخدم الجداول المرجعية** للمراجعة السريعة

---

---

## 🔗 **روابط سياقية مفيدة**

### **للتطبيق العملي**
- **تشفير قيصر**: راجع [مثال 1-2](./examples.md#مثال-1-تشفير-قيصر-الأساسي) في الأمثلة العملية
- **القسمة مع الأعداد السالبة**: راجع [مثال 6](./examples.md#مثال-6-القسمة-مع-عدد-سالب-حالة-معقدة)
- **خوارزمية إقليدس**: راجع [أمثلة 10-11](./examples.md#مثال-10-gcd-لعددين-صغيرين)
- **المعكوسات الضربية**: راجع [مثال 22](./examples.md#مثال-22-استخدام-خوارزمية-إقليدس-الموسعة)

### **للمراجعة السريعة**
- **جداول العمليات النمطية**: راجع [quick-reference.md](./quick-reference.md#جداول-العمليات-النمطية)
- **جداول المعكوسات**: راجع [quick-reference.md](./quick-reference.md#جداول-المعكوسات)
- **خرائط ذهنية**: راجع [quick-reference.md](./quick-reference.md#الخرائط-الذهنية)

### **للتدريب والممارسة**
- **تمارين أساسية**: ابدأ بـ [التمارين الأساسية](./exercises.md#تمارين-أساسية---المفاهيم)
- **تمارين متوسطة**: انتقل لـ [التمارين المتوسطة](./exercises.md#تمارين-متوسطة---التطبيقات)
- **أسئلة شاملة**: اختبر نفسك مع [الأسئلة الشاملة](./exercises.md#أسئلة-مراجعة-شاملة)

---

## ⬅️➡️ **أزرار التنقل**

| ⬅️ **السابق** | 🏠 **الرئيسية** | ➡️ **التالي** |
|:---:|:---:|:---:|
| [الفهرس الرئيسي](./index.md) | [index.md](./index.md) | [الأمثلة العملية](./examples.md) |
| *نقطة البداية* | *جميع الملفات* | *التطبيق العملي* |

---

**📝 ملاحظة**: هذا الملف جزء من نظام CICS المتكامل. استخدم [الفهرس الرئيسي](./index.md) للتنقل بين جميع الملفات حسب هدفك التعليمي.
