# 📊 **ملف المراجع السريعة - المحاضرة الثانية في التشفير**
## **جداول وخرائط ذهنية للمراجعة السريعة**

---

## 📋 **فهرس المراجع**

| القسم | المحتوى | الصفحة |
|-------|---------|--------|
| 🔐 | [مصطلحات التشفير الأساسية](#مصطلحات-التشفير-الأساسية) | 2 |
| 📐 | [القوانين الرياضية الأساسية](#القوانين-الرياضية-الأساسية) | 4 |
| 📊 | [جداول العمليات النمطية](#جداول-العمليات-النمطية) | 6 |
| ↔️ | [جداول المعكوسات](#جداول-المعكوسات) | 8 |
| 🧮 | [خوارزميات مهمة](#خوارزميات-مهمة) | 10 |
| 🗺️ | [الخرائط الذهنية](#الخرائط-الذهنية) | 12 |

---

## 🔐 **مصطلحات التشفير الأساسية**

### **📖 قاموس المصطلحات**

| المصطلح العربي | المصطلح الإنجليزي | الرمز | التعريف المختصر |
|----------------|-------------------|------|------------------|
| **التشفير** | Cryptography | - | علم وفن إنشاء الشيفرات السرية |
| **النص الأصلي** | Plaintext | P, M | الرسالة الأصلية قبل التشفير |
| **النص المشفر** | Ciphertext | C | الرسالة بعد التشفير |
| **المفتاح السري** | Secret Key | K | القيمة المستخدمة في التشفير |
| **التشفير** | Encryption | E | عملية تحويل النص الأصلي لمشفر |
| **فك التشفير** | Decryption | D | عملية استعادة النص الأصلي |
| **نظام التشفير** | Cryptosystem | - | مجموعة الخوارزميات والإجراءات |
| **تحليل الشفرة** | Cryptanalysis | - | علم كسر الشيفرات |
| **علم التشفير** | Cryptology | - | التشفير + تحليل الشفرة |

### **🏷️ تصنيفات التشفير**

#### **حسب استخدام الحاسوب**
| النوع | الخصائص | الأمثلة |
|-------|---------|---------|
| **كلاسيكي** | يدوي، بسيط، سهل الكسر | قيصر، فيجينير، بلايفير |
| **حديث** | حاسوبي، معقد، آمن | AES، RSA، ECC |

#### **حسب عدد المفاتيح**
| النوع | عدد المفاتيح | الاستخدام الرئيسي | المميزات | العيوب |
|-------|-------------|------------------|----------|--------|
| **متماثل** | 1 | تشفير البيانات | سريع، بسيط | صعوبة توزيع المفاتيح |
| **غير متماثل** | 2 | تبادل المفاتيح | سهولة التوزيع | بطيء، معقد |
| **هاش** | 0 | التحقق من السلامة | سريع، آمن | اتجاه واحد فقط |

---

## 📐 **القوانين الرياضية الأساسية**

### **🧮 قوانين القسمة الصحيحة**

| القانون | الصيغة | الشرط | المثال |
|---------|--------|--------|---------|
| **القسمة الأساسية** | `a = q × n + r` | `0 ≤ r < |n|` | `17 = 3×5 + 2` |
| **العامل النمطي** | `a mod n = r` | `n > 0` | `17 mod 5 = 2` |
| **التطابق** | `a ≡ b (mod n)` | `a mod n = b mod n` | `17 ≡ 2 (mod 5)` |

### **🔍 قوانين القابلية للقسمة**

| الخاصية | الصيغة الرياضية | المثال |
|---------|-----------------|---------|
| **الخاصية 1** | `إذا كان a|1، فإن a = ±1` | `1|1، (-1)|1` |
| **الخاصية 2** | `إذا كان a|b و b|a، فإن a = ±b` | `6|6، (-6)|6` |
| **الخاصية 3** | `إذا كان a|b و b|c، فإن a|c` | `3|6، 6|12 ⟹ 3|12` |
| **الخاصية 4** | `إذا كان a|b و a|c، فإن a|(mb+nc)` | `5|10، 5|15 ⟹ 5|40` |

### **🔄 قوانين الحساب النمطي**

| العملية | القانون | المثال |
|---------|---------|---------|
| **الجمع** | `(a + b) mod n = ((a mod n) + (b mod n)) mod n` | `(13 + 17) mod 5 = (3 + 2) mod 5 = 0` |
| **الطرح** | `(a - b) mod n = ((a mod n) - (b mod n)) mod n` | `(13 - 17) mod 5 = (3 - 2) mod 5 = 1` |
| **الضرب** | `(a × b) mod n = ((a mod n) × (b mod n)) mod n` | `(13 × 17) mod 5 = (3 × 2) mod 5 = 1` |
| **الأس** | `a^k mod n = ((a mod n)^k) mod n` | `13³ mod 5 = 3³ mod 5 = 2` |

### **↔️ قوانين المعكوسات**

| نوع المعكوس | الشرط | الصيغة | المثال |
|-------------|--------|--------|---------|
| **جمعي** | دائماً موجود | `a + (-a) ≡ 0 (mod n)` | `3 + 7 ≡ 0 (mod 10)` |
| **ضربي** | `gcd(a, n) = 1` | `a × a⁻¹ ≡ 1 (mod n)` | `3 × 7 ≡ 1 (mod 10)` |

---

## 📊 **جداول العمليات النمطية**

### **🔢 جدول الجمع في Z₅**
```
+  | 0  1  2  3  4
---|---------------
0  | 0  1  2  3  4
1  | 1  2  3  4  0
2  | 2  3  4  0  1
3  | 3  4  0  1  2
4  | 4  0  1  2  3
```

### **🔢 جدول الضرب في Z₅**
```
×  | 0  1  2  3  4
---|---------------
0  | 0  0  0  0  0
1  | 0  1  2  3  4
2  | 0  2  4  1  3
3  | 0  3  1  4  2
4  | 0  4  3  2  1
```

### **🔢 جدول الجمع في Z₇**
```
+  | 0  1  2  3  4  5  6
---|--------------------
0  | 0  1  2  3  4  5  6
1  | 1  2  3  4  5  6  0
2  | 2  3  4  5  6  0  1
3  | 3  4  5  6  0  1  2
4  | 4  5  6  0  1  2  3
5  | 5  6  0  1  2  3  4
6  | 6  0  1  2  3  4  5
```

### **🔢 جدول الضرب في Z₇**
```
×  | 0  1  2  3  4  5  6
---|--------------------
0  | 0  0  0  0  0  0  0
1  | 0  1  2  3  4  5  6
2  | 0  2  4  6  1  3  5
3  | 0  3  6  2  5  1  4
4  | 0  4  1  5  2  6  3
5  | 0  5  3  1  6  4  2
6  | 0  6  5  4  3  2  1
```

### **📈 جدول القوى في Z₁₁**
| a | a² | a³ | a⁴ | a⁵ | a⁶ | a⁷ | a⁸ | a⁹ | a¹⁰ |
|---|----|----|----|----|----|----|----|----|-----|
| 2 | 4  | 8  | 5  | 10 | 9  | 7  | 3  | 6  | 1   |
| 3 | 9  | 5  | 4  | 1  | 3  | 9  | 5  | 4  | 1   |
| 5 | 3  | 4  | 9  | 1  | 5  | 3  | 4  | 9  | 1   |
| 7 | 5  | 2  | 3  | 10 | 4  | 6  | 9  | 8  | 1   |

---

## ↔️ **جداول المعكوسات**

### **🔄 المعكوسات الجمعية**

| Zn | المعكوسات الجمعية |
|----|-------------------|
| **Z₅** | (0,0), (1,4), (2,3), (3,2), (4,1) |
| **Z₇** | (0,0), (1,6), (2,5), (3,4), (4,3), (5,2), (6,1) |
| **Z₁₀** | (0,0), (1,9), (2,8), (3,7), (4,6), (5,5), (6,4), (7,3), (8,2), (9,1) |
| **Z₁₂** | (0,0), (1,11), (2,10), (3,9), (4,8), (5,7), (6,6), (7,5), (8,4), (9,3), (10,2), (11,1) |

### **↔️ المعكوسات الضربية**

| Zn | المعكوسات الضربية |
|----|-------------------|
| **Z₅** | (1,1), (2,3), (3,2), (4,4) |
| **Z₇** | (1,1), (2,4), (3,5), (4,2), (5,3), (6,6) |
| **Z₁₁** | (1,1), (2,6), (3,4), (4,3), (5,9), (6,2), (7,8), (8,7), (9,5), (10,10) |
| **Z₁₃** | (1,1), (2,7), (3,9), (4,10), (5,8), (6,11), (7,2), (8,5), (9,3), (10,4), (11,6), (12,12) |

### **⚠️ عناصر بدون معكوس ضربي**

| Zn | العناصر بدون معكوس ضربي | السبب |
|----|-------------------------|--------|
| **Z₆** | {0, 2, 3, 4} | gcd(a, 6) ≠ 1 |
| **Z₈** | {0, 2, 4, 6} | gcd(a, 8) ≠ 1 |
| **Z₁₀** | {0, 2, 4, 5, 6, 8} | gcd(a, 10) ≠ 1 |
| **Z₁₂** | {0, 2, 3, 4, 6, 8, 9, 10} | gcd(a, 12) ≠ 1 |

---

## 🧮 **خوارزميات مهمة**

### **🔍 خوارزمية إقليدس**
```
📋 المدخلات: عددان صحيحان موجبان a, b
📤 المخرجات: gcd(a, b)

🔄 الخطوات:
1. إذا كان b = 0، أرجع a
2. وإلا، احسب r = a mod b
3. اجعل a = b, b = r
4. كرر من الخطوة 1
```

### **🔍 خوارزمية إقليدس الموسعة**
```
📋 المدخلات: عددان صحيحان موجبان a, b
📤 المخرجات: gcd(a, b), s, t حيث gcd(a, b) = s×a + t×b

🔄 الخطوات:
1. إذا كان b = 0، أرجع (a, 1, 0)
2. وإلا، احسب (g, s1, t1) = ExtendedGCD(b, a mod b)
3. احسب s = t1, t = s1 - (a ÷ b) × t1
4. أرجع (g, s, t)
```

### **🔄 خوارزمية التربيع المتكرر**
```
📋 المدخلات: الأساس a، الأس k، المعامل n
📤 المخرجات: a^k mod n

🔄 الخطوات:
1. اكتب k في النظام الثنائي
2. ابدأ بالنتيجة = 1
3. لكل بت في k (من اليسار لليمين):
   - ربع النتيجة: result = (result²) mod n
   - إذا كان البت = 1: result = (result × a) mod n
4. أرجع النتيجة
```

### **↔️ خوارزمية إيجاد المعكوس الضربي**
```
📋 المدخلات: العنصر a، المعامل n
📤 المخرجات: a⁻¹ mod n (إذا وُجد)

🔄 الخطوات:
1. احسب (g, s, t) = ExtendedGCD(n, a)
2. إذا كان g ≠ 1، أرجع "لا يوجد معكوس"
3. وإلا، أرجع t mod n
```

---

## 🗺️ **الخرائط الذهنية**

### **🔐 خريطة مفاهيم التشفير الأساسية**
```
                    🔐 التشفير
                         |
        ┌────────────────┼────────────────┐
        |                |                |
    📝 النصوص        🔑 المفاتيح      🧮 العمليات
        |                |                |
   ┌────┴────┐      ┌────┴────┐      ┌────┴────┐
   |         |      |         |      |         |
 واضح    مشفر   متماثل  غير متماثل  تشفير  فك تشفير
   |         |      |         |      |         |
   P         C      K         K₁,K₂    E(P,K)   D(C,K)
```

### **🧮 خريطة العمليات الرياضية**
```
                🧮 الرياضيات في التشفير
                         |
        ┌────────────────┼────────────────┐
        |                |                |
    🔢 الأعداد       ➗ القسمة        🔄 النمطي
    الصحيحة                              |
        |                |           ┌────┴────┐
   ┌────┴────┐      ┌────┴────┐      |         |
   |         |      |         |      |         |
   Z      عمليات   قسمة    قابلية   Zn     عمليات
 {...}   +,-,×    a=qn+r  للقسمة   {0..n-1}  mod
```

### **🔍 خريطة خوارزمية إقليدس**
```
            🔍 خوارزمية إقليدس
                    |
        ┌───────────┼───────────┐
        |           |           |
    📊 الأساسية  📊 الموسعة   🎯 التطبيقات
        |           |           |
   ┌────┴────┐ ┌────┴────┐ ┌────┴────┐
   |         | |         | |         |
 gcd(a,b)   | s,t      | معكوسات  حل معادلات
   |         | |         | |         |
 gcd(a,0)=a | sa+tb=gcd | a⁻¹ mod n | ax≡b(mod n)
```

### **↔️ خريطة المعكوسات**
```
                ↔️ المعكوسات
                     |
        ┌────────────┼────────────┐
        |            |            |
    ➕ جمعية      ✖️ ضربية     🎯 التطبيقات
        |            |            |
   ┌────┴────┐  ┌────┴────┐  ┌────┴────┐
   |         |  |         |  |         |
 دائماً     | شرطية     | فك التشفير حل معادلات
 موجودة    |  gcd=1     |            |
   |         |  |         |  |         |
 a+(-a)≡0   | a×a⁻¹≡1   | D(C,K)=P  | ax≡b
```

---

## 📋 **جداول مرجعية سريعة**

### **🔢 أعداد أولية صغيرة**
```
2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97
```

### **🧮 قوى العدد 2**
| 2ⁿ | القيمة | 2ⁿ mod 7 | 2ⁿ mod 11 | 2ⁿ mod 13 |
|----|--------|----------|-----------|-----------|
| 2¹ | 2 | 2 | 2 | 2 |
| 2² | 4 | 4 | 4 | 4 |
| 2³ | 8 | 1 | 8 | 8 |
| 2⁴ | 16 | 2 | 5 | 3 |
| 2⁵ | 32 | 4 | 10 | 6 |
| 2⁶ | 64 | 1 | 9 | 12 |
| 2⁷ | 128 | 2 | 7 | 11 |
| 2⁸ | 256 | 4 | 3 | 9 |

### **📊 جدول gcd للأعداد الصغيرة**
```
gcd | 2  3  4  5  6  7  8  9  10
----|---------------------------
2   | 2  1  2  1  2  1  2  1  2
3   | 1  3  1  1  3  1  1  3  1
4   | 2  1  4  1  2  1  4  1  2
5   | 1  1  1  5  1  1  1  1  5
6   | 2  3  2  1  6  1  2  3  2
7   | 1  1  1  1  1  7  1  1  1
8   | 2  1  4  1  2  1  8  1  2
9   | 1  3  1  1  3  1  1  9  1
10  | 2  1  2  5  2  1  2  1  10
```

---

## 🎯 **نصائح للمراجعة السريعة**

### **📝 قائمة التحقق قبل الامتحان**
- [ ] حفظ تعريفات المصطلحات الأساسية
- [ ] فهم الفرق بين أنواع التشفير
- [ ] إتقان خوارزمية إقليدس والموسعة
- [ ] حفظ خصائص العامل النمطي
- [ ] معرفة شروط وجود المعكوسات
- [ ] التدرب على حل المعادلات النمطية

### **⚡ طرق الحفظ السريع**
1. **للمصطلحات**: استخدم الاختصارات والربط
2. **للقوانين**: اربطها بأمثلة عملية
3. **للجداول**: ابحث عن الأنماط والتماثل
4. **للخوارزميات**: تدرب على التطبيق خطوة بخطوة

### **🔍 علامات التحقق من الحلول**
- ✅ النتائج في المجال المطلوب (0 ≤ r < n)
- ✅ التحقق من الشروط (مثل gcd = 1 للمعكوسات)
- ✅ اختبار النتيجة بالتعويض العكسي
- ✅ منطقية النتيجة مع السياق

---

## 📚 **مراجع إضافية**

### **🔗 الملفات ذات الصلة**
- `summary.md`: التلخيص النظري الشامل
- `examples.md`: أمثلة محلولة تفصيلياً
- `exercises.md`: تمارين للتدريب والممارسة

### **📖 مصادر للتوسع**
- كتب التشفير الأساسية
- مواقع التدريب على الرياضيات
- برامج حاسوبية للتحقق من الحلول

---

**📝 ملاحظة**: هذا الملف مصمم للمراجعة السريعة. للفهم التفصيلي، راجع الملفات الأخرى في المجموعة.

**📅 آخر تحديث**: حسب طريقة CICS المطورة
**🎯 الهدف**: مرجع سريع شامل للمحاضرة الثانية في التشفير
