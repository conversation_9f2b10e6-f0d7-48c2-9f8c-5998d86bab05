# 📝 **ملف التمارين - المحاضرة الثانية في التشفير**
## **مسائل متدرجة الصعوبة وأسئلة مراجعة شاملة**

---

## 📋 **فهرس التمارين**

| المستوى | القسم | عدد التمارين | الصفحة |
|---------|-------|-------------|--------|
| 🟢 | [تمارين أساسية - المفاهيم](#تمارين-أساسية---المفاهيم) | 10 | 2 |
| 🟡 | [تمارين متوسطة - التطبيقات](#تمارين-متوسطة---التطبيقات) | 15 | 5 |
| 🔴 | [تمارين متقدمة - حل المسائل](#تمارين-متقدمة---حل-المسائل) | 10 | 9 |
| 🎯 | [أسئلة مراجعة شاملة](#أسئلة-مراجعة-شاملة) | 8 | 12 |

---

## 🎨 **رموز مستويات الصعوبة**

| الرمز | المستوى | الوصف | الوقت المقترح |
|-------|---------|--------|----------------|
| 🟢 | أساسي | تطبيق مباشر للمفاهيم | 2-5 دقائق |
| 🟡 | متوسط | تطبيق مع خطوات متعددة | 5-10 دقائق |
| 🔴 | متقدم | تحليل وحل مسائل معقدة | 10-20 دقيقة |
| 🎯 | شامل | مراجعة عامة ومفاهيم مترابطة | 15-30 دقيقة |

---

## 🟢 **تمارين أساسية - المفاهيم**

### **📚 القسم الأول: مصطلحات التشفير**

#### **🔐 تمرين 1**
اربط كل مصطلح بتعريفه الصحيح:

| المصطلح | التعريف |
|---------|----------|
| أ) Plaintext | 1) النسخة المشفرة من الرسالة |
| ب) Ciphertext | 2) عملية تحويل النص المشفر للأصلي |
| ج) Encryption | 3) الرسالة الأصلية قبل التشفير |
| د) Decryption | 4) عملية تحويل النص الأصلي لمشفر |

**💡 الحل**: أ-3، ب-1، ج-4، د-2

#### **🔐 تمرين 2**
صنف أنواع التشفير التالية حسب عدد المفاتيح:
- AES
- RSA  
- SHA-256
- DES
- ECC

**💡 الحل**: 
- متماثل: AES، DES
- غير متماثل: RSA، ECC
- هاش: SHA-256

#### **🔐 تمرين 3**
أكمل الجملة: "في التشفير المتماثل، يستخدم _____ مفتاح للتشفير و_____ مفتاح لفك التشفير."

**💡 الحل**: نفس، نفس

### **📚 القسم الثاني: الحساب الصحيح**

#### **🧮 تمرين 4**
احسب خارج القسمة والباقي:
- أ) 47 ÷ 6
- ب) 123 ÷ 17
- ج) -25 ÷ 7

**💡 الحل**:
- أ) q = 7, r = 5 (47 = 7×6 + 5)
- ب) q = 7, r = 4 (123 = 7×17 + 4)  
- ج) q = -4, r = 3 (-25 = -4×7 + 3)

#### **🧮 تمرين 5**
احسب العمليات التالية:
- أ) 23 mod 7
- ب) -15 mod 11
- ج) 100 mod 13

**💡 الحل**:
- أ) 23 mod 7 = 2
- ب) -15 mod 11 = 7
- ج) 100 mod 13 = 9

#### **🧮 تمرين 6**
حدد أي من العبارات التالية صحيحة:
- أ) 6 | 24
- ب) 7 | 50
- ج) 12 | 144
- د) 5 | 33

**💡 الحل**: أ) صحيح، ب) خطأ، ج) صحيح، د) خطأ

### **📚 القسم الثالث: القاسم المشترك الأكبر**

#### **🔍 تمرين 7**
احسب gcd للأزواج التالية:
- أ) gcd(12, 8)
- ب) gcd(15, 25)
- ج) gcd(17, 19)

**💡 الحل**:
- أ) gcd(12, 8) = 4
- ب) gcd(15, 25) = 5
- ج) gcd(17, 19) = 1

#### **🔍 تمرين 8**
أي من الأزواج التالية أولية نسبياً؟
- أ) (14, 21)
- ب) (9, 16)
- ج) (25, 49)

**💡 الحل**: ب) (9, 16) فقط، لأن gcd(9, 16) = 1

### **📚 القسم الرابع: الحساب النمطي**

#### **🔄 تمرين 9**
احسب العمليات التالية في Z₁₁:
- أ) (7 + 9) mod 11
- ب) (13 - 5) mod 11
- ج) (6 × 8) mod 11

**💡 الحل**:
- أ) (7 + 9) mod 11 = 5
- ب) (13 - 5) mod 11 = 8
- ج) (6 × 8) mod 11 = 4

#### **↔️ تمرين 10**
أوجد المعكوس الجمعي لكل عنصر في Z₆:

**💡 الحل**:
- 0 → 0، 1 → 5، 2 → 4، 3 → 3، 4 → 2، 5 → 1

---

## 🟡 **تمارين متوسطة - التطبيقات**

### **📚 القسم الأول: تطبيقات التشفير**

#### **🔐 تمرين 11**
شفّر النص "MATH" باستخدام تشفير قيصر مع إزاحة 13.

**🔧 خطوات الحل**:
1. تحويل الأحرف لأرقام: M=12, A=0, T=19, H=7
2. تطبيق الإزاحة: (x + 13) mod 26
3. تحويل النتائج لأحرف

**💡 الحل**: "ZNGU"

#### **🔐 تمرين 12**
إذا كان النص المشفر "KHOOR" نتج من تشفير قيصر، وكان النص الأصلي يبدأ بـ "H"، ما هو مفتاح الإزاحة؟

**🔧 خطوات الحل**:
1. K → H يعني إزاحة بـ -3 (أو +23)
2. التحقق من باقي الأحرف

**💡 الحل**: الإزاحة = -3، النص الأصلي = "HELLO"

#### **🔐 تمرين 13**
قارن بين استخدام التشفير المتماثل وغير المتماثل في الحالات التالية:
- أ) تشفير ملف كبير (1 GB)
- ب) إرسال مفتاح سري لأول مرة
- ج) التوقيع الرقمي على وثيقة

**💡 الحل**:
- أ) متماثل (للسرعة)
- ب) غير متماثل (لتبادل المفاتيح)
- ج) غير متماثل (للتوقيع)

### **📚 القسم الثاني: تطبيقات الحساب الصحيح**

#### **🧮 تمرين 14**
في نظام تشفير، نريد تقسيم رسالة طولها 1337 حرف إلى كتل حجم 23 حرف. احسب:
- أ) عدد الكتل الكاملة
- ب) عدد الأحرف في الكتلة الأخيرة
- ج) عدد أحرف الحشو المطلوبة

**💡 الحل**:
- أ) 58 كتلة كاملة
- ب) 3 أحرف
- ج) 20 حرف حشو

#### **🧮 تمرين 15**
أثبت أنه إذا كان a | b و a | c، فإن a | (2b + 3c).

**🔧 خطوات الحل**:
1. بما أن a | b، فإن b = ka لعدد صحيح k
2. بما أن a | c، فإن c = ma لعدد صحيح m
3. إذن 2b + 3c = 2ka + 3ma = a(2k + 3m)
4. بما أن (2k + 3m) عدد صحيح، فإن a | (2b + 3c)

### **📚 القسم الثالث: تطبيقات خوارزمية إقليدس**

#### **🔍 تمرين 16**
استخدم خوارزمية إقليدس لحساب gcd(252, 105).

**🔧 خطوات الحل**:
```
252 = 2 × 105 + 42
105 = 2 × 42 + 21  
42 = 2 × 21 + 0
```

**💡 الحل**: gcd(252, 105) = 21

#### **🔍 تمرين 17**
استخدم خوارزمية إقليدس الموسعة لإيجاد s و t بحيث:
gcd(56, 21) = s × 56 + t × 21

**💡 الحل**: gcd(56, 21) = 7, s = 2, t = -5

#### **🔍 تمرين 18**
أوجد جميع الحلول للمعادلة 12x + 18y = 6.

**🔧 خطوات الحل**:
1. gcd(12, 18) = 6
2. بما أن 6 | 6، فالمعادلة لها حلول
3. تبسيط: 2x + 3y = 1
4. إيجاد حل خاص ثم الحل العام

**💡 الحل**: x = 2 + 3t, y = -1 - 2t (حيث t عدد صحيح)

### **📚 القسم الرابع: تطبيقات الحساب النمطي**

#### **🔄 تمرين 19**
احسب 3¹⁰⁰ mod 7 باستخدام التربيع المتكرر.

**🔧 خطوات الحل**:
1. 100 = 64 + 32 + 4 (النظام الثنائي: 1100100)
2. حساب 3^(2^k) mod 7 للقوى المطلوبة
3. ضرب النتائج

**💡 الحل**: 3¹⁰⁰ mod 7 = 4

#### **🔄 تمرين 20**
حل المعادلة 5x ≡ 3 (mod 8).

**🔧 خطوات الحل**:
1. التحقق: gcd(5, 8) = 1 ✓
2. إيجاد 5⁻¹ mod 8 = 5
3. ضرب الطرفين في 5

**💡 الحل**: x ≡ 7 (mod 8)

#### **🔄 تمرين 21**
أوجد جميع المعكوسات الضربية في Z₁₅.

**💡 الحل**: 
العناصر التي لها معكوسات: {1, 2, 4, 7, 8, 11, 13, 14}
الأزواج: (1,1), (2,8), (4,4), (7,13), (8,2), (11,11), (13,7), (14,14)

### **📚 القسم الخامس: مسائل تطبيقية**

#### **🎯 تمرين 22**
في خوارزمية RSA المبسطة، إذا كان p = 7, q = 11:
- أ) احسب n و φ(n)
- ب) اختر قيمة مناسبة لـ e
- ج) احسب d

**💡 الحل**:
- أ) n = 77, φ(n) = 60
- ب) e = 13 (مثلاً)
- ج) d = 37

#### **🎯 تمرين 23**
في تشفير أفيني C ≡ (ax + b) mod 26، إذا كان a = 7, b = 3:
- أ) تحقق من صحة المعاملات
- ب) شفّر الحرف 'E'
- ج) أوجد معادلة فك التشفير

**💡 الحل**:
- أ) gcd(7, 26) = 1 ✓
- ب) E → H
- ج) x ≡ 15(y - 3) mod 26

#### **🎯 تمرين 24**
أوجد عدد المفاتيح الممكنة في تشفير أفيني للأبجدية الإنجليزية.

**🔧 خطوات الحل**:
1. عدد القيم الممكنة لـ a: φ(26) = 12
2. عدد القيم الممكنة لـ b: 26
3. العدد الكلي: 12 × 26 = 312

**💡 الحل**: 312 مفتاح

#### **🎯 تمرين 25**
في نظام تشفير، نستخدم الحساب في Z₂₉. أوجد عدد العناصر التي لها معكوس ضربي.

**💡 الحل**: 28 عنصر (جميع العناصر عدا 0، لأن 29 عدد أولي)

---

## 🔴 **تمارين متقدمة - حل المسائل**

### **📚 القسم الأول: مسائل تحليلية**

#### **🎯 تمرين 26**
أثبت أنه إذا كان gcd(a, n) = 1، فإن المعادلة ax ≡ b (mod n) لها حل وحيد في Zn.

**🔧 خطوات البرهان**:
1. الوجود: بما أن gcd(a, n) = 1، فإن a له معكوس ضربي
2. الوحدانية: إذا كان x₁, x₂ حلين، فإن a(x₁ - x₂) ≡ 0 (mod n)
3. بما أن gcd(a, n) = 1، فإن x₁ ≡ x₂ (mod n)

#### **🎯 تمرين 27**
أوجد جميع الحلول للنظام:
```
x ≡ 2 (mod 5)
x ≡ 3 (mod 7)
x ≡ 1 (mod 11)
```

**🔧 خطوات الحل** (نظرية الباقي الصيني):
1. التحقق من أن المعاملات أولية نسبياً
2. تطبيق خوارزمية نظرية الباقي الصيني
3. إيجاد الحل العام

**💡 الحل**: x ≡ 177 (mod 385)

#### **🎯 تمرين 28**
في Z₁₀₀، كم عنصر له معكوس ضربي؟ اذكر هذه العناصر.

**🔧 خطوات الحل**:
1. العناصر التي لها معكوس هي التي gcd(a, 100) = 1
2. حساب φ(100) = φ(4) × φ(25) = 2 × 20 = 40
3. تحديد العناصر الفعلية

**💡 الحل**: 40 عنصر

### **📚 القسم الثاني: مسائل خوارزمية**

#### **🎯 تمرين 29**
صمم خوارزمية فعالة لحساب aⁿ mod m حيث n عدد كبير جداً.

**💡 الحل**: خوارزمية التربيع المتكرر مع تحسينات

#### **🎯 تمرين 30**
أثبت أن خوارزمية إقليدس تنتهي في عدد خطوات لا يتجاوز 5 × log₁₀(min(a,b)).

**🔧 فكرة البرهان**:
استخدام خصائص متتالية فيبوناتشي وتحليل التعقيد

### **📚 القسم الثالث: مسائل تطبيقية معقدة**

#### **🎯 تمرين 31**
في نظام RSA، إذا كان n = 3233 و e = 17:
- أ) احسب φ(n) (علماً أن n = 53 × 61)
- ب) احسب d
- ج) شفّر الرسالة m = 123
- د) فك تشفير النتيجة للتأكد

**💡 الحل**:
- أ) φ(3233) = 3120
- ب) d = 2753
- ج) c = 855
- د) m = 123 ✓

#### **🎯 تمرين 32**
صمم نظام تشفير هجين يجمع بين التشفير المتماثل وغير المتماثل لإرسال ملف حجمه 10 MB.

**🔧 خطوات التصميم**:
1. توليد مفتاح متماثل عشوائي
2. تشفير الملف بالمفتاح المتماثل
3. تشفير المفتاح المتماثل بالمفتاح العام
4. إرسال كلا النتيجتين

#### **🎯 تمرين 33**
حلل أمان تشفير قيصر ضد الهجمات التالية:
- أ) هجوم القوة الغاشمة
- ب) تحليل التكرار
- ج) هجوم النص المعروف

**💡 التحليل**:
- أ) 26 احتمال فقط - سهل جداً
- ب) تحليل تكرار الأحرف يكشف المفتاح
- ج) معرفة زوج واحد (نص أصلي، مشفر) يكشف المفتاح

#### **🎯 تمرين 34**
أثبت أن عدد المفاتيح في تشفير الاستبدال البسيط للأبجدية الإنجليزية هو 26!.

**🔧 البرهان**:
كل مفتاح هو تبديل (permutation) للأحرف الـ26، وعدد التبديلات = 26!

#### **🎯 تمرين 35**
في نظام تشفير يستخدم Z₂₅₆، أوجد عدد العناصر التي لها معكوس ضربي واشرح لماذا هذا العدد مهم في التشفير.

**💡 الحل**: φ(256) = 128 عنصر
**الأهمية**: يحدد عدد المفاتيح الصالحة في بعض أنظمة التشفير

---

## 🎯 **أسئلة مراجعة شاملة**

### **📚 السؤال الأول: مفاهيم أساسية (20 درجة)**
أ) عرّف المصطلحات التالية مع إعطاء مثال لكل منها: (10 درجات)
   - التشفير المتماثل
   - المعكوس الضربي
   - القاسم المشترك الأكبر

ب) قارن بين التشفير الكلاسيكي والحديث من حيث: (10 درجات)
   - طريقة التنفيذ
   - مستوى الأمان
   - سرعة المعالجة

### **📚 السؤال الثاني: حسابات رياضية (25 درجة)**
أ) احسب gcd(1071, 462) باستخدام خوارزمية إقليدس. (8 درجات)

ب) أوجد قيم s و t بحيث gcd(1071, 462) = s×1071 + t×462. (8 درجات)

ج) احسب 7²³ mod 13 باستخدام التربيع المتكرر. (9 درجات)

### **📚 السؤال الثالث: المعكوسات (20 درجة)**
أ) أوجد جميع المعكوسات الضربية في Z₁₆. (10 درجات)

ب) حل المعادلة 11x ≡ 7 (mod 15). (10 درجات)

### **📚 السؤال الرابع: تطبيقات التشفير (25 درجة)**
أ) شفّر النص "SECURITY" باستخدام تشفير أفيني مع a = 5, b = 8. (10 درجات)

ب) في نظام RSA مبسط مع p = 11, q = 13:
   - احسب n و φ(n) (5 درجات)
   - اختر قيمة مناسبة لـ e واحسب d (5 درجات)
   - شفّر الرسالة m = 42 (5 درجات)

### **📚 السؤال الخامس: حل مسائل (10 درجات)**
في نظام اتصالات، نريد تقسيم رسالة طولها 2048 بت إلى كتل حجم 64 بت لتشفيرها. إذا كانت آخر كتلة تحتوي على 23 بت فقط:
أ) كم كتلة كاملة سنحصل عليها؟
ب) كم بت حشو نحتاج لإضافتها؟
ج) ما هو الطول الكلي للرسالة بعد الحشو؟

---

## 📝 **إجابات مختارة للتمارين المتقدمة**

### **🎯 إجابة السؤال الثاني**
أ) gcd(1071, 462) = 21
ب) s = -13, t = 30
ج) 7²³ mod 13 = 7

### **🎯 إجابة السؤال الثالث**
أ) العناصر التي لها معكوسات في Z₁₆: {1, 3, 5, 7, 9, 11, 13, 15}
ب) x ≡ 8 (mod 15)

### **🎯 إجابة السؤال الخامس**
أ) 32 كتلة كاملة
ب) 41 بت حشو
ج) 2112 بت

---

## 💡 **نصائح لحل التمارين**

### **🎯 استراتيجيات الحل**
1. **اقرأ السؤال بعناية** وحدد المطلوب بدقة
2. **اكتب المعطيات** والقوانين ذات الصلة
3. **خطط للحل** قبل البدء في الحسابات
4. **تحقق من النتيجة** بطريقة مختلفة إن أمكن
5. **راجع الخطوات** للتأكد من عدم وجود أخطاء

### **⚠️ أخطاء شائعة يجب تجنبها**
- عدم التحقق من شروط وجود المعكوسات
- الخطأ في التعامل مع الأعداد السالبة في mod
- عدم تبسيط النتائج للمجال المطلوب
- الخلط بين العمليات في Z و Zn
- عدم التحقق من صحة النتيجة النهائية

### **🔧 أدوات مساعدة**
- استخدم الآلة الحاسبة للعمليات الكبيرة
- ارسم جداول للعمليات الصغيرة
- استخدم الجداول المرجعية في `quick-reference.md`
- تدرب على الأمثلة في `examples.md`

---

## 📚 **مصادر إضافية للتدريب**

### **🔗 الملفات ذات الصلة**
- `summary.md`: للمراجعة النظرية
- `examples.md`: لأمثلة محلولة مفصلة
- `quick-reference.md`: للجداول والمراجع السريعة

### **📖 مواقع مفيدة للتدريب**
- Khan Academy (للرياضيات الأساسية)
- Coursera Cryptography Courses
- مواقع التدريب على الحساب النمطي

### **💻 برامج للتحقق من الحلول**
- Python (للحسابات الرياضية)
- Wolfram Alpha (للتحقق السريع)
- حاسبات الحساب النمطي عبر الإنترنت

---

**📝 ملاحظة نهائية**: هذه التمارين مصممة لتغطية جميع جوانب المحاضرة الثانية. ابدأ بالتمارين الأساسية وتدرج للمتقدمة حسب مستوى فهمك.

**🎯 هدف التمارين**: بناء فهم عميق وقدرة على التطبيق العملي لمفاهيم التشفير ورياضياته.

**📅 وقت الإنجاز المقترح**: 3-4 ساعات للتمارين الأساسية، 4-6 ساعات للمتوسطة، 6-8 ساعات للمتقدمة.
